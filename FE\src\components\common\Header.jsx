import React from 'react';
import { Link } from 'react-router-dom';

// Component Header cho phần client
const Header = () => {
  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="text-2xl font-bold text-blue-600">
            E-Commerce
          </Link>

          {/* Navigation Menu - ẩn trên mobile */}
          <nav className="hidden md:flex space-x-6">
            <Link to="/" className="text-gray-700 hover:text-blue-600">
              Trang chủ
            </Link>
            <Link to="/products" className="text-gray-700 hover:text-blue-600">
              Sản phẩm
            </Link>
            <Link to="/cart" className="text-gray-700 hover:text-blue-600">
              Giỏ hàng
            </Link>
          </nav>

          {/* Auth Links */}
          <div className="flex items-center space-x-4">
            <Link to="/auth/login" className="text-gray-700 hover:text-blue-600">
              Đ<PERSON>ng nhập
            </Link>
            <Link to="/auth/register" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
              Đăng ký
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
