!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactHookForm={},e.React)}(this,function(e,t){"use strict";var r=e=>"checkbox"===e.type,s=e=>e instanceof Date,a=e=>null==e;const i=e=>"object"==typeof e;var n=e=>!a(e)&&!Array.isArray(e)&&i(e)&&!s(e),o=e=>n(e)&&e.target?r(e.target)?e.target.checked:e.target.value:e,l=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),u="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function d(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else{if(u&&(e instanceof Blob||s)||!r&&!n(e))return e;if(t=r?[]:Object.create(Object.getPrototypeOf(e)),r||(e=>{const t=e.constructor&&e.constructor.prototype;return n(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=d(e[r]));else t=e}return t}var c=e=>/^\w*$/.test(e),f=e=>void 0===e,m=e=>Array.isArray(e)?e.filter(Boolean):[],y=e=>m(e.replace(/["|']|\]/g,"").split(/\.|\[/)),g=(e,t,r)=>{if(!t||!n(e))return r;const s=(c(t)?[t]:y(t)).reduce((e,t)=>a(e)?e:e[t],e);return f(s)||s===e?f(e[t])?r:e[t]:s},_=e=>"boolean"==typeof e,b=(e,t,r)=>{let s=-1;const a=c(t)?[t]:y(t),i=a.length,o=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==o){const r=e[t];i=n(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const p="blur",h="focusout",v="change",V="onBlur",F="onChange",A="onSubmit",x="onTouched",S="all",k="max",w="min",D="maxLength",C="minLength",E="pattern",O="required",j="validate",M=t.createContext(null);M.displayName="HookFormContext";const T=()=>t.useContext(M);var U=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==S&&(t._proxyFormState[a]=!s||S),r&&(r[a]=!0),e[a]}});return a};const R="undefined"!=typeof window?t.useLayoutEffect:t.useEffect;function N(e){const r=T(),{control:s=r.control,disabled:a,name:i,exact:n}=e||{},[o,l]=t.useState(s._formState),u=t.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return R(()=>s._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...s._formState,...e})}}),[i,a,n]),t.useEffect(()=>{u.current.isValid&&s._setValid(!0)},[s]),t.useMemo(()=>U(o,s,u.current,!1),[o,s])}var B=e=>"string"==typeof e,L=(e,t,r,s,a)=>B(e)?(s&&t.watch.add(e),g(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),g(r,e))):(s&&(t.watchAll=!0),r),P=e=>a(e)||!i(e);function I(e,t,r=new WeakSet){if(P(e)||P(t))return e===t;if(s(e)&&s(t))return e.getTime()===t.getTime();const a=Object.keys(e),i=Object.keys(t);if(a.length!==i.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const o of a){const a=e[o];if(!i.includes(o))return!1;if("ref"!==o){const e=t[o];if(s(a)&&s(e)||n(a)&&n(e)||Array.isArray(a)&&Array.isArray(e)?!I(a,e,r):a!==e)return!1}}return!0}function W(e){const r=T(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o,compute:l}=e||{},u=t.useRef(i),d=t.useRef(l),c=t.useRef(void 0);d.current=l;const f=t.useMemo(()=>s._getWatch(a,u.current),[s,a]),[m,y]=t.useState(d.current?d.current(f):f);return R(()=>s._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>{if(!n){const t=L(a,s._names,e.values||s._formValues,!1,u.current);if(d.current){const e=d.current(t);I(e,c.current)||(y(e),c.current=e)}else y(t)}}}),[s,n,a,o]),t.useEffect(()=>s._removeUnmounted()),m}function q(e){const r=T(),{name:s,disabled:a,control:i=r.control,shouldUnregister:n,defaultValue:u}=e,c=l(i._names.array,s),m=t.useMemo(()=>g(i._formValues,s,g(i._defaultValues,s,u)),[i,s,u]),y=W({control:i,name:s,defaultValue:m,exact:!0}),h=N({control:i,name:s,exact:!0}),V=t.useRef(e),F=t.useRef(i.register(s,{...e.rules,value:y,..._(e.disabled)?{disabled:e.disabled}:{}}));V.current=e;const A=t.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!g(h.errors,s)},isDirty:{enumerable:!0,get:()=>!!g(h.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!g(h.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!g(h.validatingFields,s)},error:{enumerable:!0,get:()=>g(h.errors,s)}}),[h,s]),x=t.useCallback(e=>F.current.onChange({target:{value:o(e),name:s},type:v}),[s]),S=t.useCallback(()=>F.current.onBlur({target:{value:g(i._formValues,s),name:s},type:p}),[s,i._formValues]),k=t.useCallback(e=>{const t=g(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[i._fields,s]),w=t.useMemo(()=>({name:s,value:y,..._(a)||h.disabled?{disabled:h.disabled||a}:{},onChange:x,onBlur:S,ref:k}),[s,a,h.disabled,x,S,k,y]);return t.useEffect(()=>{const e=i._options.shouldUnregister||n;i.register(s,{...V.current.rules,..._(V.current.disabled)?{disabled:V.current.disabled}:{}});const t=(e,t)=>{const r=g(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=d(g(i._options.defaultValues,s));b(i._defaultValues,s,e),f(g(i._formValues,s))&&b(i._formValues,s,e)}return!c&&i.register(s),()=>{(c?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}},[s,i,c,n]),t.useEffect(()=>{i._setDisabledField({disabled:a,name:s})},[a,s,i]),t.useMemo(()=>({field:w,formState:h,fieldState:A}),[w,h,A])}const $=e=>{const t={};for(const r of Object.keys(e))if(i(e[r])&&null!==e[r]){const s=$(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},H="post";var J=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},z=e=>Array.isArray(e)?e:[e],G=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},K=e=>n(e)&&!Object.keys(e).length,Q=e=>"file"===e.type,X=e=>"function"==typeof e,Y=e=>{if(!u)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Z=e=>"select-multiple"===e.type,ee=e=>"radio"===e.type,te=e=>Y(e)&&e.isConnected;function re(e,t){const r=Array.isArray(t)?t:c(t)?[t]:y(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=f(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(n(s)&&K(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!f(e[t]))return!1;return!0}(s))&&re(e,r.slice(0,-1)),e}var se=e=>{for(const t in e)if(X(e[t]))return!0;return!1};function ae(e,t={}){const r=Array.isArray(e);if(n(e)||r)for(const r in e)Array.isArray(e[r])||n(e[r])&&!se(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ae(e[r],t[r])):a(e[r])||(t[r]=!0);return t}function ie(e,t,r){const s=Array.isArray(e);if(n(e)||s)for(const s in e)Array.isArray(e[s])||n(e[s])&&!se(e[s])?f(t)||P(r[s])?r[s]=Array.isArray(e[s])?ae(e[s],[]):{...ae(e[s])}:ie(e[s],a(t)?{}:t[s],r[s]):r[s]=!I(e[s],t[s]);return r}var ne=(e,t)=>ie(e,t,ae(t));const oe={value:!1,isValid:!1},le={value:!0,isValid:!0};var ue=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!f(e[0].attributes.value)?f(e[0].value)||""===e[0].value?le:{value:e[0].value,isValid:!0}:le:oe}return oe},de=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>f(e)?e:t?""===e?NaN:e?+e:e:r&&B(e)?new Date(e):s?s(e):e;const ce={isValid:!1,value:null};var fe=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,ce):ce;function me(e){const t=e.ref;return Q(t)?t.files:ee(t)?fe(e.refs).value:Z(t)?[...t.selectedOptions].map(({value:e})=>e):r(t)?ue(e.refs).value:de(f(t.value)?e.ref.value:t.value,e)}var ye=e=>e instanceof RegExp,ge=e=>f(e)?e:ye(e)?e.source:n(e)?ye(e.value)?e.value.source:e.value:e,_e=e=>({isOnSubmit:!e||e===A,isOnBlur:e===V,isOnChange:e===F,isOnAll:e===S,isOnTouch:e===x});const be="AsyncFunction";var pe=e=>!!e&&!!e.validate&&!!(X(e.validate)&&e.validate.constructor.name===be||n(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===be)),he=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const ve=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=g(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(ve(i,t))break}else if(n(i)&&ve(i,t))break}}};function Ve(e,t,r){const s=g(e,r);if(s||c(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=g(t,s),n=g(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var Fe=(e,t,r)=>{const s=z(g(e,r));return b(s,"root",t[r]),b(e,r,s),e},Ae=e=>B(e);function xe(e,t,r="validate"){if(Ae(e)||Array.isArray(e)&&e.every(Ae)||_(e)&&!e)return{type:r,message:Ae(e)?e:"",ref:t}}var Se=e=>n(e)&&!ye(e)?e:{value:e,message:""},ke=async(e,t,s,i,o,l)=>{const{ref:u,refs:d,required:c,maxLength:m,minLength:y,min:b,max:p,pattern:h,validate:v,name:V,valueAsNumber:F,mount:A}=e._f,x=g(s,V);if(!A||t.has(V))return{};const S=d?d[0]:u,M=e=>{o&&S.reportValidity&&(S.setCustomValidity(_(e)?"":e||""),S.reportValidity())},T={},U=ee(u),R=r(u),N=U||R,L=(F||Q(u))&&f(u.value)&&f(x)||Y(u)&&""===u.value||""===x||Array.isArray(x)&&!x.length,P=J.bind(null,V,i,T),I=(e,t,r,s=D,a=C)=>{const i=e?t:r;T[V]={type:e?s:a,message:i,ref:u,...P(e?s:a,i)}};if(l?!Array.isArray(x)||!x.length:c&&(!N&&(L||a(x))||_(x)&&!x||R&&!ue(d).isValid||U&&!fe(d).isValid)){const{value:e,message:t}=Ae(c)?{value:!!c,message:c}:Se(c);if(e&&(T[V]={type:O,message:t,ref:S,...P(O,t)},!i))return M(t),T}if(!(L||a(b)&&a(p))){let e,t;const r=Se(p),s=Se(b);if(a(x)||isNaN(x)){const a=u.valueAsDate||new Date(x),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;B(r.value)&&x&&(e=n?i(x)>i(r.value):o?x>r.value:a>new Date(r.value)),B(s.value)&&x&&(t=n?i(x)<i(s.value):o?x<s.value:a<new Date(s.value))}else{const i=u.valueAsNumber||(x?+x:x);a(r.value)||(e=i>r.value),a(s.value)||(t=i<s.value)}if((e||t)&&(I(!!e,r.message,s.message,k,w),!i))return M(T[V].message),T}if((m||y)&&!L&&(B(x)||l&&Array.isArray(x))){const e=Se(m),t=Se(y),r=!a(e.value)&&x.length>+e.value,s=!a(t.value)&&x.length<+t.value;if((r||s)&&(I(r,e.message,t.message),!i))return M(T[V].message),T}if(h&&!L&&B(x)){const{value:e,message:t}=Se(h);if(ye(e)&&!x.match(e)&&(T[V]={type:E,message:t,ref:u,...P(E,t)},!i))return M(t),T}if(v)if(X(v)){const e=xe(await v(x,s),S);if(e&&(T[V]={...e,...P(j,e.message)},!i))return M(e.message),T}else if(n(v)){let e={};for(const t in v){if(!K(e)&&!i)break;const r=xe(await v[t](x,s),S,t);r&&(e={...r,...P(t,r.message)},M(r.message),i&&(T[V]=e))}if(!K(e)&&(T[V]={ref:S,...e},!i))return T}return M(!0),T};const we={mode:A,reValidateMode:F,shouldFocusError:!0};function De(e={}){let t,i={...we,...e},c={submitCount:0,isDirty:!1,isReady:!1,isLoading:X(i.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:i.errors||{},disabled:i.disabled||!1},y={},v=(n(i.defaultValues)||n(i.values))&&d(i.defaultValues||i.values)||{},V=i.shouldUnregister?{}:d(v),F={action:!1,mount:!1,watch:!1},A={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const k={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let w={...k};const D={array:G(),state:G()},C=i.criteriaMode===S,E=async e=>{if(!i.disabled&&(k.isValid||w.isValid||e)){const e=i.resolver?K((await U()).errors):await R(y,!0);e!==c.isValid&&D.state.next({isValid:e})}},O=(e,t)=>{!i.disabled&&(k.isValidating||k.validatingFields||w.isValidating||w.validatingFields)&&((e||Array.from(A.mount)).forEach(e=>{e&&(t?b(c.validatingFields,e,t):re(c.validatingFields,e))}),D.state.next({validatingFields:c.validatingFields,isValidating:!K(c.validatingFields)}))},j=(e,t,r,s)=>{const a=g(y,e);if(a){const i=g(V,e,f(r)?g(v,e):r);f(i)||s&&s.defaultChecked||t?b(V,e,t?i:me(a._f)):W(e,i),F.mount&&E()}},M=(e,t,r,s,a)=>{let n=!1,o=!1;const l={name:e};if(!i.disabled){if(!r||s){(k.isDirty||w.isDirty)&&(o=c.isDirty,c.isDirty=l.isDirty=N(),n=o!==l.isDirty);const r=I(g(v,e),t);o=!!g(c.dirtyFields,e),r?re(c.dirtyFields,e):b(c.dirtyFields,e,!0),l.dirtyFields=c.dirtyFields,n=n||(k.dirtyFields||w.dirtyFields)&&o!==!r}if(r){const t=g(c.touchedFields,e);t||(b(c.touchedFields,e,r),l.touchedFields=c.touchedFields,n=n||(k.touchedFields||w.touchedFields)&&t!==r)}n&&a&&D.state.next(l)}return n?l:{}},T=(e,r,s,a)=>{const n=g(c.errors,e),o=(k.isValid||w.isValid)&&_(r)&&c.isValid!==r;var l;if(i.delayError&&s?(l=()=>((e,t)=>{b(c.errors,e,t),D.state.next({errors:c.errors})})(e,s),t=e=>{clearTimeout(x),x=setTimeout(l,e)},t(i.delayError)):(clearTimeout(x),t=null,s?b(c.errors,e,s):re(c.errors,e)),(s?!I(n,s):n)||!K(a)||o){const t={...a,...o&&_(r)?{isValid:r}:{},errors:c.errors,name:e};c={...c,...t},D.state.next(t)}},U=async e=>{O(e,!0);const t=await i.resolver(V,i.context,((e,t,r,s)=>{const a={};for(const r of e){const e=g(t,r);e&&b(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||A.mount,y,i.criteriaMode,i.shouldUseNativeValidation));return O(e),t},R=async(e,t,r={valid:!0})=>{for(const s in e){const a=e[s];if(a){const{_f:e,...n}=a;if(e){const n=A.array.has(e.name),o=a._f&&pe(a._f);o&&k.validatingFields&&O([s],!0);const l=await ke(a,A.disabled,V,C,i.shouldUseNativeValidation&&!t,n);if(o&&k.validatingFields&&O([s]),l[e.name]&&(r.valid=!1,t))break;!t&&(g(l,e.name)?n?Fe(c.errors,l,e.name):b(c.errors,e.name,l[e.name]):re(c.errors,e.name))}!K(n)&&await R(n,t,r)}}return r.valid},N=(e,t)=>!i.disabled&&(e&&t&&b(V,e,t),!I(ae(),v)),P=(e,t,r)=>L(e,A,{...F.mount?V:f(t)?v:B(e)?{[e]:t}:t},r,t),W=(e,t,s={})=>{const i=g(y,e);let n=t;if(i){const s=i._f;s&&(!s.disabled&&b(V,e,de(t,s)),n=Y(s.ref)&&a(t)?"":t,Z(s.ref)?[...s.ref.options].forEach(e=>e.selected=n.includes(e.value)):s.refs?r(s.ref)?s.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(n)?e.checked=!!n.find(t=>t===e.value):e.checked=n===e.value||!!n)}):s.refs.forEach(e=>e.checked=e.value===n):Q(s.ref)?s.ref.value="":(s.ref.value=n,s.ref.type||D.state.next({name:e,values:d(V)})))}(s.shouldDirty||s.shouldTouch)&&M(e,n,s.shouldTouch,s.shouldDirty,!0),s.shouldValidate&&se(e)},q=(e,t,r)=>{for(const a in t){if(!t.hasOwnProperty(a))return;const i=t[a],o=e+"."+a,l=g(y,o);(A.array.has(e)||n(i)||l&&!l._f)&&!s(i)?q(o,i,r):W(o,i,r)}},$=(e,t,r={})=>{const s=g(y,e),i=A.array.has(e),n=d(t);b(V,e,n),i?(D.array.next({name:e,values:d(V)}),(k.isDirty||k.dirtyFields||w.isDirty||w.dirtyFields)&&r.shouldDirty&&D.state.next({name:e,dirtyFields:ne(v,V),isDirty:N(e,n)})):!s||s._f||a(n)?W(e,n,r):q(e,n,r),he(e,A)&&D.state.next({...c,name:e}),D.state.next({name:F.mount?e:void 0,values:d(V)})},H=async e=>{F.mount=!0;const r=e.target;let a=r.name,n=!0;const l=g(y,a),u=e=>{n=Number.isNaN(e)||s(e)&&isNaN(e.getTime())||I(e,g(V,a,e))},f=_e(i.mode),m=_e(i.reValidateMode);if(l){let s,v;const F=r.type?me(l._f):o(e),x=e.type===p||e.type===h,S=!((_=l._f).mount&&(_.required||_.min||_.max||_.maxLength||_.minLength||_.pattern||_.validate)||i.resolver||g(c.errors,a)||l._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(x,g(c.touchedFields,a),c.isSubmitted,m,f),j=he(a,A,x);b(V,a,F),x?r&&r.readOnly||(l._f.onBlur&&l._f.onBlur(e),t&&t(0)):l._f.onChange&&l._f.onChange(e);const N=M(a,F,x),B=!K(N)||j;if(!x&&D.state.next({name:a,type:e.type,values:d(V)}),S)return(k.isValid||w.isValid)&&("onBlur"===i.mode?x&&E():x||E()),B&&D.state.next({name:a,...j?{}:N});if(!x&&j&&D.state.next({...c}),i.resolver){const{errors:e}=await U([a]);if(u(F),n){const t=Ve(c.errors,y,a),r=Ve(e,y,t.name||a);s=r.error,a=r.name,v=K(e)}}else O([a],!0),s=(await ke(l,A.disabled,V,C,i.shouldUseNativeValidation))[a],O([a]),u(F),n&&(s?v=!1:(k.isValid||w.isValid)&&(v=await R(y,!0)));n&&(l._f.deps&&se(l._f.deps),T(a,v,s,N))}var _},J=(e,t)=>{if(g(c.errors,t)&&e.focus)return e.focus(),1},se=async(e,t={})=>{let r,s;const a=z(e);if(i.resolver){const t=await(async e=>{const{errors:t}=await U(e);if(e)for(const r of e){const e=g(t,r);e?b(c.errors,r,e):re(c.errors,r)}else c.errors=t;return t})(f(e)?e:a);r=K(t),s=e?!a.some(e=>g(t,e)):r}else e?(s=(await Promise.all(a.map(async e=>{const t=g(y,e);return await R(t&&t._f?{[e]:t}:t)}))).every(Boolean),(s||c.isValid)&&E()):s=r=await R(y);return D.state.next({...!B(e)||(k.isValid||w.isValid)&&r!==c.isValid?{}:{name:e},...i.resolver||!e?{isValid:r}:{},errors:c.errors}),t.shouldFocus&&!s&&ve(y,J,e?a:A.mount),s},ae=e=>{const t={...F.mount?V:v};return f(e)?t:B(e)?g(t,e):e.map(e=>g(t,e))},ie=(e,t)=>({invalid:!!g((t||c).errors,e),isDirty:!!g((t||c).dirtyFields,e),error:g((t||c).errors,e),isValidating:!!g(c.validatingFields,e),isTouched:!!g((t||c).touchedFields,e)}),oe=(e,t,r)=>{const s=(g(y,e,{_f:{}})._f||{}).ref,a=g(c.errors,e)||{},{ref:i,message:n,type:o,...l}=a;b(c.errors,e,{...l,...t,ref:s}),D.state.next({name:e,errors:c.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},le=e=>D.state.subscribe({next:t=>{var r,s,a;r=e.name,s=t.name,a=e.exact,r&&s&&r!==s&&!z(r).some(e=>e&&(a?e===s:e.startsWith(s)||s.startsWith(e)))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return K(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||S))})(t,e.formState||k,Se,e.reRenderRoot)||e.callback({values:{...V},...c,...t,defaultValues:v})}}).unsubscribe,ue=(e,t={})=>{for(const r of e?z(e):A.mount)A.mount.delete(r),A.array.delete(r),t.keepValue||(re(y,r),re(V,r)),!t.keepError&&re(c.errors,r),!t.keepDirty&&re(c.dirtyFields,r),!t.keepTouched&&re(c.touchedFields,r),!t.keepIsValidating&&re(c.validatingFields,r),!i.shouldUnregister&&!t.keepDefaultValue&&re(v,r);D.state.next({values:d(V)}),D.state.next({...c,...t.keepDirty?{isDirty:N()}:{}}),!t.keepIsValid&&E()},ce=({disabled:e,name:t})=>{(_(e)&&F.mount||e||A.disabled.has(t))&&(e?A.disabled.add(t):A.disabled.delete(t))},fe=(e,t={})=>{let s=g(y,e);const a=_(t.disabled)||_(i.disabled);return b(y,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...t}}),A.mount.add(e),s?ce({disabled:_(t.disabled)?t.disabled:i.disabled,name:e}):j(e,!0,t.value),{...a?{disabled:t.disabled||i.disabled}:{},...i.progressive?{required:!!t.required,min:ge(t.min),max:ge(t.max),minLength:ge(t.minLength),maxLength:ge(t.maxLength),pattern:ge(t.pattern)}:{},name:e,onChange:H,onBlur:H,ref:a=>{if(a){fe(e,t),s=g(y,e);const i=f(a.value)&&a.querySelectorAll&&a.querySelectorAll("input,select,textarea")[0]||a,n=(e=>ee(e)||r(e))(i),o=s._f.refs||[];if(n?o.find(e=>e===i):i===s._f.ref)return;b(y,e,{_f:{...s._f,...n?{refs:[...o.filter(te),i,...Array.isArray(g(v,e))?[{}]:[]],ref:{type:i.type,name:e}}:{ref:i}}}),j(e,!1,void 0,i)}else s=g(y,e,{}),s._f&&(s._f.mount=!1),(i.shouldUnregister||t.shouldUnregister)&&(!l(A.array,e)||!F.action)&&A.unMount.add(e)}}},ye=()=>i.shouldFocusError&&ve(y,J,A.mount),be=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=d(V);if(D.state.next({isSubmitting:!0}),i.resolver){const{errors:e,values:t}=await U();c.errors=e,a=d(t)}else await R(y);if(A.disabled.size)for(const e of A.disabled)re(a,e);if(re(c.errors,"root"),K(c.errors)){D.state.next({errors:{}});try{await e(a,r)}catch(e){s=e}}else t&&await t({...c.errors},r),ye(),setTimeout(ye);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:K(c.errors)&&!s,submitCount:c.submitCount+1,errors:c.errors}),s)throw s},Ae=(e,t={})=>{const r=e?d(e):v,s=d(r),a=K(e),n=a?v:s;if(t.keepDefaultValues||(v=r),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...A.mount,...Object.keys(ne(v,V))]);for(const t of Array.from(e))g(c.dirtyFields,t)?b(n,t,g(V,t)):$(t,g(n,t))}else{if(u&&f(e))for(const e of A.mount){const t=g(y,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(Y(e)){const t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(const e of A.mount)$(e,g(n,e));else y={}}V=i.shouldUnregister?t.keepDefaultValues?d(v):{}:d(n),D.array.next({values:{...n}}),D.state.next({values:{...n}})}A={mount:t.keepDirtyValues?A.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},F.mount=!k.isValid||!!t.keepIsValid||!!t.keepDirtyValues,F.watch=!!i.shouldUnregister,D.state.next({submitCount:t.keepSubmitCount?c.submitCount:0,isDirty:!a&&(t.keepDirty?c.isDirty:!(!t.keepDefaultValues||I(e,v))),isSubmitted:!!t.keepIsSubmitted&&c.isSubmitted,dirtyFields:a?{}:t.keepDirtyValues?t.keepDefaultValues&&V?ne(v,V):c.dirtyFields:t.keepDefaultValues&&e?ne(v,e):t.keepDirty?c.dirtyFields:{},touchedFields:t.keepTouched?c.touchedFields:{},errors:t.keepErrors?c.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&c.isSubmitSuccessful,isSubmitting:!1,defaultValues:v})},xe=(e,t)=>Ae(X(e)?e(V):e,t),Se=e=>{c={...c,...e}},De={control:{register:fe,unregister:ue,getFieldState:ie,handleSubmit:be,setError:oe,_subscribe:le,_runSchema:U,_focusError:ye,_getWatch:P,_getDirty:N,_setValid:E,_setFieldArray:(e,t=[],r,s,a=!0,n=!0)=>{if(s&&r&&!i.disabled){if(F.action=!0,n&&Array.isArray(g(y,e))){const t=r(g(y,e),s.argA,s.argB);a&&b(y,e,t)}if(n&&Array.isArray(g(c.errors,e))){const t=r(g(c.errors,e),s.argA,s.argB);a&&b(c.errors,e,t),((e,t)=>{!m(g(e,t)).length&&re(e,t)})(c.errors,e)}if((k.touchedFields||w.touchedFields)&&n&&Array.isArray(g(c.touchedFields,e))){const t=r(g(c.touchedFields,e),s.argA,s.argB);a&&b(c.touchedFields,e,t)}(k.dirtyFields||w.dirtyFields)&&(c.dirtyFields=ne(v,V)),D.state.next({name:e,isDirty:N(e,t),dirtyFields:c.dirtyFields,errors:c.errors,isValid:c.isValid})}else b(V,e,t)},_setDisabledField:ce,_setErrors:e=>{c.errors=e,D.state.next({errors:c.errors,isValid:!1})},_getFieldArray:e=>m(g(F.mount?V:v,e,i.shouldUnregister?g(v,e,[]):[])),_reset:Ae,_resetDefaultValues:()=>X(i.defaultValues)&&i.defaultValues().then(e=>{xe(e,i.resetOptions),D.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of A.unMount){const t=g(y,e);t&&(t._f.refs?t._f.refs.every(e=>!te(e)):!te(t._f.ref))&&ue(e)}A.unMount=new Set},_disableForm:e=>{_(e)&&(D.state.next({disabled:e}),ve(y,(t,r)=>{const s=g(y,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:D,_proxyFormState:k,get _fields(){return y},get _formValues(){return V},get _state(){return F},set _state(e){F=e},get _defaultValues(){return v},get _names(){return A},set _names(e){A=e},get _formState(){return c},get _options(){return i},set _options(e){i={...i,...e}}},subscribe:e=>(F.mount=!0,w={...w,...e.formState},le({...e,formState:w})),trigger:se,register:fe,handleSubmit:be,watch:(e,t)=>X(e)?D.state.subscribe({next:r=>"values"in r&&e(P(void 0,t),r)}):P(e,t,!0),setValue:$,getValues:ae,reset:xe,resetField:(e,t={})=>{g(y,e)&&(f(t.defaultValue)?$(e,d(g(v,e))):($(e,t.defaultValue),b(v,e,d(t.defaultValue))),t.keepTouched||re(c.touchedFields,e),t.keepDirty||(re(c.dirtyFields,e),c.isDirty=t.defaultValue?N(e,d(g(v,e))):N()),t.keepError||(re(c.errors,e),k.isValid&&E()),D.state.next({...c}))},clearErrors:e=>{e&&z(e).forEach(e=>re(c.errors,e)),D.state.next({errors:e?c.errors:{}})},unregister:ue,setError:oe,setFocus:(e,t={})=>{const r=g(y,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&X(e.select)&&e.select())}},getFieldState:ie};return{...De,formControl:De}}var Ce=()=>{if("undefined"!=typeof crypto&&crypto.randomUUID)return crypto.randomUUID();const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)})},Ee=(e,t,r={})=>r.shouldFocus||f(r.shouldFocus)?r.focusName||`${e}.${f(r.focusIndex)?t:r.focusIndex}.`:"",Oe=(e,t)=>[...e,...z(t)],je=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Me(e,t,r){return[...e.slice(0,t),...z(r),...e.slice(t)]}var Te=(e,t,r)=>Array.isArray(e)?(f(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Ue=(e,t)=>[...z(t),...z(e)];var Re=(e,t)=>f(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return m(s).length?s:[]}(e,z(t).sort((e,t)=>e-t)),Ne=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},Be=(e,t,r)=>(e[t]=r,e);e.Controller=e=>e.render(q(e)),e.Form=function(e){const r=T(),[s,a]=t.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:l,method:u=H,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,..._}=e,b=async t=>{let r=!1,s="";await i.handleSubmit(async e=>{const a=new FormData;let o="";try{o=JSON.stringify(e)}catch(e){}const m=$(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:e,event:t,method:u,formData:a,formDataJson:o}),l)try{const e=[d&&d["Content-Type"],c].some(e=>e&&e.includes("json")),t=await fetch(String(l),{method:u,headers:{...d,...c&&"multipart/form-data"!==c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}})(t),r&&e.control&&(e.control._subjects.state.next({isSubmitSuccessful:!1}),e.control.setError("root.server",{type:s}))};return t.useEffect(()=>{a(!0)},[]),m?t.createElement(t.Fragment,null,m({submit:b})):t.createElement("form",{noValidate:s,action:l,method:u,encType:c,onSubmit:b,..._},o)},e.FormProvider=e=>{const{children:r,...s}=e;return t.createElement(M.Provider,{value:s},r)},e.appendErrors=J,e.createFormControl=De,e.get=g,e.set=b,e.useController=q,e.useFieldArray=function(e){const r=T(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=e,[l,u]=t.useState(s._getFieldArray(a)),c=t.useRef(s._getFieldArray(a).map(Ce)),f=t.useRef(l),m=t.useRef(!1);f.current=l,s._names.array.add(a),t.useMemo(()=>o&&s.register(a,o),[s,o,a]),R(()=>s._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===a||!t){const t=g(e,a);Array.isArray(t)&&(u(t),c.current=t.map(Ce))}}}).unsubscribe,[s,a]);const y=t.useCallback(e=>{m.current=!0,s._setFieldArray(a,e)},[s,a]);return t.useEffect(()=>{if(s._state.action=!1,he(a,s._names)&&s._subjects.state.next({...s._formState}),m.current&&(!_e(s._options.mode).isOnSubmit||s._formState.isSubmitted)&&!_e(s._options.reValidateMode).isOnSubmit)if(s._options.resolver)s._runSchema([a]).then(e=>{const t=g(e.errors,a),r=g(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?b(s._formState.errors,a,t):re(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))});else{const e=g(s._fields,a);!e||!e._f||_e(s._options.reValidateMode).isOnSubmit&&_e(s._options.mode).isOnSubmit||ke(e,s._names.disabled,s._formValues,s._options.criteriaMode===S,s._options.shouldUseNativeValidation,!0).then(e=>!K(e)&&s._subjects.state.next({errors:Fe(s._formState.errors,e,a)}))}s._subjects.state.next({name:a,values:d(s._formValues)}),s._names.focus&&ve(s._fields,(e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1}),s._names.focus="",s._setValid(),m.current=!1},[l,a,s]),t.useEffect(()=>(!g(s._formValues,a)&&s._setFieldArray(a),()=>{s._options.shouldUnregister||n?s.unregister(a):((e,t)=>{const r=g(s._fields,e);r&&r._f&&(r._f.mount=t)})(a,!1)}),[a,s,i,n]),{swap:t.useCallback((e,t)=>{const r=s._getFieldArray(a);Ne(r,e,t),Ne(c.current,e,t),y(r),u(r),s._setFieldArray(a,r,Ne,{argA:e,argB:t},!1)},[y,a,s]),move:t.useCallback((e,t)=>{const r=s._getFieldArray(a);Te(r,e,t),Te(c.current,e,t),y(r),u(r),s._setFieldArray(a,r,Te,{argA:e,argB:t},!1)},[y,a,s]),prepend:t.useCallback((e,t)=>{const r=z(d(e)),i=Ue(s._getFieldArray(a),r);s._names.focus=Ee(a,0,t),c.current=Ue(c.current,r.map(Ce)),y(i),u(i),s._setFieldArray(a,i,Ue,{argA:je(e)})},[y,a,s]),append:t.useCallback((e,t)=>{const r=z(d(e)),i=Oe(s._getFieldArray(a),r);s._names.focus=Ee(a,i.length-1,t),c.current=Oe(c.current,r.map(Ce)),y(i),u(i),s._setFieldArray(a,i,Oe,{argA:je(e)})},[y,a,s]),remove:t.useCallback(e=>{const t=Re(s._getFieldArray(a),e);c.current=Re(c.current,e),y(t),u(t),!Array.isArray(g(s._fields,a))&&b(s._fields,a,void 0),s._setFieldArray(a,t,Re,{argA:e})},[y,a,s]),insert:t.useCallback((e,t,r)=>{const i=z(d(t)),n=Me(s._getFieldArray(a),e,i);s._names.focus=Ee(a,e,r),c.current=Me(c.current,e,i.map(Ce)),y(n),u(n),s._setFieldArray(a,n,Me,{argA:e,argB:je(t)})},[y,a,s]),update:t.useCallback((e,t)=>{const r=d(t),i=Be(s._getFieldArray(a),e,r);c.current=[...i].map((t,r)=>t&&r!==e?c.current[r]:Ce()),y(i),u([...i]),s._setFieldArray(a,i,Be,{argA:e,argB:r},!0,!1)},[y,a,s]),replace:t.useCallback(e=>{const t=z(d(e));c.current=t.map(Ce),y([...t]),u([...t]),s._setFieldArray(a,[...t],e=>e,{},!0,!1)},[y,a,s]),fields:t.useMemo(()=>l.map((e,t)=>({...e,[i]:c.current[t]||Ce()})),[l,i])}},e.useForm=function(e={}){const r=t.useRef(void 0),s=t.useRef(void 0),[a,i]=t.useState({isDirty:!1,isValidating:!1,isLoading:X(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:X(e.defaultValues)?void 0:e.defaultValues});if(!r.current)if(e.formControl)r.current={...e.formControl,formState:a},e.defaultValues&&!X(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:t,...s}=De(e);r.current={...s,formState:a}}const n=r.current.control;return n._options=e,R(()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),n._formState.isReady=!0,e},[n]),t.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),t.useEffect(()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)},[n,e.mode,e.reValidateMode]),t.useEffect(()=>{e.errors&&(n._setErrors(e.errors),n._focusError())},[n,e.errors]),t.useEffect(()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,e.shouldUnregister]),t.useEffect(()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}},[n,a.isDirty]),t.useEffect(()=>{e.values&&!I(e.values,s.current)?(n._reset(e.values,{keepFieldsRef:!0,...n._options.resetOptions}),s.current=e.values,i(e=>({...e}))):n._resetDefaultValues()},[n,e.values]),t.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),r.current.formState=U(a,n),r.current},e.useFormContext=T,e.useFormState=N,e.useWatch=W});
//# sourceMappingURL=index.umd.js.map
