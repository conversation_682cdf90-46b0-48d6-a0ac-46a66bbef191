import { Routes, Route, Navigate } from 'react-router-dom';

// Import các route components từ folder client
import ClientRoutes from './client';
import AuthRoutes from './client/authRoutes';
import UserRoutes from './client/userRoutes';

// Import các route components từ folder admin
import AdminRoutes from './admin';

// Component ch<PERSON>h quản lý tất cả routes của ứng dụng
const AppRoutes = () => {
  return (
    <Routes>
      {/* Routes cho phần client (không yêu cầu đăng nhập) */}
      {ClientRoutes()}

      {/* Routes cho xác thực */}
      {AuthRoutes()}

      {/* Routes cho người dùng đã đăng nhập */}
      {UserRoutes()}

      {/* Routes cho phần admin */}
      {AdminRoutes()}

      {/* Redirect các route không tồn tại về trang chủ */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default AppRoutes;
