import { Routes, Route, Navigate } from 'react-router-dom';

// Import các route components
import ClientRoutes from './ClientRoutes';
import AdminRoutes from './AdminRoutes';
import AuthRoutes from './AuthRoutes';

// Component ch<PERSON>h quản lý tất cả routes của ứng dụng
const AppRoutes = () => {
  return (
    <Routes>
      {/* Routes cho phần client */}
      {ClientRoutes()}
      
      {/* Routes cho xác thực */}
      {AuthRoutes()}
      
      {/* Routes cho phần admin */}
      {AdminRoutes()}
      
      {/* Redirect các route không tồn tại về trang chủ */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default AppRoutes;
