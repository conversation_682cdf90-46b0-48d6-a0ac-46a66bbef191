!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("effect"),require("effect/ParseResult"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","effect","effect/ParseResult","react-hook-form"],r):r((e||self).hookformResolversEffectTs={},e.hookformResolvers,e.Effect,e.EffectParseResult,e.ReactHookForm)}(this,function(e,r,t,o,f){e.effectTsResolver=function(e,n){return void 0===n&&(n={errors:"all",onExcessProperty:"ignore"}),function(s,a,i){return o.decodeUnknown(e,n)(s).pipe(t.Effect.catchAll(function(e){return t.Effect.flip(o.ArrayFormatter.formatIssue(e))}),t.Effect.mapError(function(e){var t=!i.shouldUseNativeValidation&&"all"===i.criteriaMode,o=e.reduce(function(e,r){var o=r.path.join(".");if(e[o]||(e[o]={message:r.message,type:r._tag}),t){var n=e[o].types,s=n&&n[String(r._tag)];e[o]=f.appendErrors(o,t,e,r._tag,s?[].concat(s,r.message):r.message)}return e},{});return r.toNestErrors(o,i)}),t.Effect.tap(function(){return t.Effect.sync(function(){return i.shouldUseNativeValidation&&r.validateFieldsNatively({},i)})}),t.Effect.match({onFailure:function(e){return{errors:e,values:{}}},onSuccess:function(e){return{errors:{},values:e}}}),t.Effect.runPromise)}}});
//# sourceMappingURL=effect-ts.umd.js.map
