# E-Commerce Frontend Project Structure

## Cấu trúc thư mục chuyên nghiệp

```
FE/
├── public/                     # Static files
│   ├── vite.svg
│   └── index.html
├── src/
│   ├── components/             # Reusable components
│   │   ├── common/            # Common components
│   │   │   ├── Header.jsx     # Header component
│   │   │   ├── Footer.jsx     # Footer component
│   │   │   └── Loading.jsx    # Loading spinner
│   │   ├── client/            # Client-specific components
│   │   └── admin/             # Admin-specific components
│   ├── layouts/               # Layout components
│   │   ├── ClientLayout.jsx   # Client layout wrapper
│   │   └── AdminLayout.jsx    # Admin layout wrapper
│   ├── pages/                 # Page components
│   │   ├── client/            # Client pages
│   │   │   ├── Home.jsx       # Homepage
│   │   │   ├── Products.jsx   # Products listing
│   │   │   ├── ProductDetail.jsx # Product detail
│   │   │   ├── Cart.jsx       # Shopping cart
│   │   │   ├── Checkout.jsx   # Checkout process
│   │   │   ├── Login.jsx      # Login page
│   │   │   └── Register.jsx   # Registration page
│   │   └── admin/             # Admin pages
│   │       ├── Dashboard.jsx  # Admin dashboard
│   │       ├── ProductsManagement.jsx # Product management
│   │       ├── Orders.jsx     # Order management
│   │       └── Users.jsx      # User management
│   ├── routes/                # Route configuration
│   │   ├── client/            # Client routes
│   │   │   ├── index.jsx      # Main client routes
│   │   │   ├── authRoutes.jsx # Auth routes
│   │   │   ├── userRoutes.jsx # User routes
│   │   │   └── index.js       # Export file
│   │   ├── admin/             # Admin routes
│   │   │   ├── index.jsx      # Main admin routes
│   │   │   ├── productRoutes.jsx # Product management routes
│   │   │   ├── orderRoutes.jsx # Order management routes
│   │   │   └── index.js       # Export file
│   │   ├── AppRoutes.jsx      # Main routes component
│   │   ├── ProtectedRoute.jsx # Route protection
│   │   ├── RouteConfig.js     # Route configuration
│   │   └── index.js           # Export all routes
│   ├── services/              # API services
│   │   ├── api.js            # Axios configuration
│   │   ├── productService.js  # Product API calls
│   │   ├── cartService.js     # Cart API calls
│   │   ├── authService.js     # Authentication API calls
│   │   └── orderService.js    # Order API calls
│   ├── store/                 # Redux store
│   │   ├── store.js          # Store configuration
│   │   └── slices/           # Redux slices
│   │       ├── authSlice.js   # Authentication state
│   │       ├── cartSlice.js   # Cart state
│   │       └── productSlice.js # Product state
│   ├── hooks/                 # Custom React hooks
│   ├── utils/                 # Utility functions
│   │   ├── constants.js       # Application constants
│   │   └── helpers.js         # Helper functions
│   ├── App.jsx               # Main App component
│   ├── main.jsx              # Entry point
│   └── index.css             # Global styles
├── package.json              # Dependencies
├── vite.config.js           # Vite configuration
├── tailwind.config.js       # Tailwind CSS configuration
└── eslint.config.js         # ESLint configuration
```

## Công nghệ sử dụng

### Core Technologies
- **React 19** - Frontend framework
- **Vite** - Build tool và dev server
- **React Router DOM** - Client-side routing
- **Redux Toolkit** - State management
- **Axios** - HTTP client

### UI & Styling
- **Tailwind CSS** - Utility-first CSS framework
- **Heroicons** - Icon library
- **Headless UI** - Unstyled, accessible UI components

### Development Tools
- **ESLint** - Code linting
- **PostCSS** - CSS processing
- **Autoprefixer** - CSS vendor prefixes

## Tính năng chính

### Client Features
- **Homepage**: Hiển thị sản phẩm nổi bật
- **Product Catalog**: Danh sách sản phẩm với filter theo danh mục
- **Product Detail**: Chi tiết sản phẩm với khả năng thêm vào giỏ hàng
- **Shopping Cart**: Quản lý giỏ hàng (thêm, sửa, xóa)
- **Checkout**: Quy trình thanh toán
- **Authentication**: Đăng nhập, đăng ký, quên mật khẩu
- **User Profile**: Quản lý thông tin cá nhân
- **Order History**: Lịch sử đơn hàng

### Admin Features
- **Dashboard**: Tổng quan thống kê
- **Product Management**: Quản lý sản phẩm (CRUD)
- **Category Management**: Quản lý danh mục
- **Order Management**: Quản lý đơn hàng
- **User Management**: Quản lý khách hàng
- **Reports**: Báo cáo doanh thu

## API Integration

### Backend Endpoints
- **Products**: `/products`, `/products/detail/:slug`, `/products/:category`
- **Cart**: `/cart`, `/cart/add`, `/cart/update-quantity`, `/cart/delete`
- **Auth**: `/auth/login`, `/auth/register`, `/auth/logout`
- **Orders**: `/order/post`, `/order/view`, `/order/detail/:id`
- **Admin**: `/admin/products`, `/admin/orders`, `/admin/users`

### State Management
- **Auth State**: User authentication, login status
- **Cart State**: Shopping cart items, quantities, total
- **Product State**: Product listings, categories, search results

## Development Guidelines

### Component Structure
- Sử dụng functional components với hooks
- Tách biệt logic và presentation
- Tái sử dụng components thông qua props

### State Management
- Sử dụng Redux cho global state
- Local state cho component-specific data
- Async operations với Redux Toolkit Query

### Styling
- Tailwind CSS cho styling
- Responsive design mobile-first
- Consistent color scheme và typography

### Code Organization
- Feature-based folder structure
- Separation of concerns
- Reusable utility functions

## Getting Started

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Environment Variables

```env
REACT_APP_API_URL=http://localhost:3000
REACT_APP_APP_NAME=E-Commerce
```
