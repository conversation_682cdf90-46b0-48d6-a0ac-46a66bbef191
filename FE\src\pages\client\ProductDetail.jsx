import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { MinusIcon, PlusIcon } from '@heroicons/react/24/outline';
import Loading from '../../components/common/Loading';
import { getProductDetail } from '../../services/productService';
import { addToCart } from '../../services/cartService';

const ProductDetail = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const response = await getProductDetail(slug);
        setProduct(response.data);
      } catch (err) {
        setError('Không thể tải thông tin sản phẩm');
        console.error('Error fetching product detail:', err);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchProduct();
    }
  }, [slug]);

  const handleQuantityChange = (type) => {
    if (type === 'increase' && quantity < product.stock) {
      setQuantity(quantity + 1);
    } else if (type === 'decrease' && quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleAddToCart = async () => {
    try {
      setAddingToCart(true);
      await addToCart(product._id, quantity);
      alert('Đã thêm sản phẩm vào giỏ hàng!');
    } catch (err) {
      alert('Không thể thêm sản phẩm vào giỏ hàng');
      console.error('Error adding to cart:', err);
    } finally {
      setAddingToCart(false);
    }
  };

  if (loading) return <Loading text="Đang tải thông tin sản phẩm..." />;
  if (error) return <div className="text-center text-red-600 py-8">{error}</div>;
  if (!product) return <div className="text-center text-gray-600 py-8">Không tìm thấy sản phẩm</div>;

  const priceNew = product.discountPercentage > 0 
    ? product.price * (100 - product.discountPercentage) / 100 
    : product.price;

  return (
    <div className="container mx-auto px-4 py-8">
      <button
        onClick={() => navigate(-1)}
        className="mb-6 text-blue-600 hover:text-blue-800 font-medium"
      >
        ← Quay lại
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Image */}
        <div className="aspect-w-1 aspect-h-1">
          <img
            src={product.thumbnail || '/placeholder-image.jpg'}
            alt={product.title}
            className="w-full h-96 object-cover rounded-lg"
          />
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {product.title}
            </h1>
            <p className="text-gray-600 leading-relaxed">
              {product.description}
            </p>
          </div>

          {/* Price */}
          <div className="flex items-center space-x-4">
            <span className="text-3xl font-bold text-red-600">
              {priceNew.toLocaleString('vi-VN')}đ
            </span>
            {product.discountPercentage > 0 && (
              <>
                <span className="text-xl text-gray-500 line-through">
                  {product.price.toLocaleString('vi-VN')}đ
                </span>
                <span className="bg-red-100 text-red-800 text-sm font-medium px-3 py-1 rounded">
                  -{product.discountPercentage}%
                </span>
              </>
            )}
          </div>

          {/* Stock */}
          <div className="text-gray-600">
            <span className="font-medium">Còn lại:</span> {product.stock} sản phẩm
          </div>

          {/* Quantity Selector */}
          <div className="flex items-center space-x-4">
            <span className="font-medium text-gray-700">Số lượng:</span>
            <div className="flex items-center border border-gray-300 rounded-lg">
              <button
                onClick={() => handleQuantityChange('decrease')}
                disabled={quantity <= 1}
                className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <MinusIcon className="w-4 h-4" />
              </button>
              <span className="px-4 py-2 font-medium">{quantity}</span>
              <button
                onClick={() => handleQuantityChange('increase')}
                disabled={quantity >= product.stock}
                className="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PlusIcon className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Add to Cart Button */}
          <div className="space-y-3">
            <button
              onClick={handleAddToCart}
              disabled={addingToCart || product.stock === 0}
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {addingToCart ? 'Đang thêm...' : 'Thêm vào giỏ hàng'}
            </button>
            
            {product.stock === 0 && (
              <p className="text-red-600 text-center font-medium">Sản phẩm đã hết hàng</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
