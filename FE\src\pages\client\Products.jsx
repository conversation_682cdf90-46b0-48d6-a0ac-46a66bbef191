import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Loading from '../../components/common/Loading';
import { getProducts, getProductsByCategory } from '../../services/productService';

const Products = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await getProducts();
        setProducts(response.data.newProducts || []);
        setCategories(response.data.layoutProductCategory || []);
      } catch (err) {
        setError('Không thể tải sản phẩm');
        console.error('Error fetching products:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const handleCategoryChange = async (categorySlug) => {
    try {
      setLoading(true);
      setSelectedCategory(categorySlug);
      
      if (categorySlug === 'all') {
        const response = await getProducts();
        setProducts(response.data.newProducts || []);
      } else {
        const response = await getProductsByCategory(categorySlug);
        setProducts(response.data.products || []);
      }
    } catch (err) {
      setError('Không thể tải sản phẩm theo danh mục');
      console.error('Error fetching products by category:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <Loading text="Đang tải sản phẩm..." />;
  if (error) return <div className="text-center text-red-600 py-8">{error}</div>;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Sản phẩm</h1>
      
      {/* Category Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => handleCategoryChange('all')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedCategory === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Tất cả
          </button>
          {categories.map((category) => (
            <button
              key={category._id}
              onClick={() => handleCategoryChange(category.slug)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category.slug
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {category.title}
            </button>
          ))}
        </div>
      </div>

      {/* Products Grid */}
      {products.length === 0 ? (
        <div className="text-center text-gray-600 py-12">
          <p className="text-xl">Không có sản phẩm nào trong danh mục này</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => (
            <div key={product._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-w-1 aspect-h-1">
                <img
                  src={product.thumbnail || '/placeholder-image.jpg'}
                  alt={product.title}
                  className="w-full h-48 object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                  {product.title}
                </h3>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {product.description}
                </p>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex flex-col">
                    {product.discountPercentage > 0 ? (
                      <>
                        <span className="text-lg font-bold text-red-600">
                          {product.priceNew?.toLocaleString('vi-VN')}đ
                        </span>
                        <span className="text-sm text-gray-500 line-through">
                          {product.price.toLocaleString('vi-VN')}đ
                        </span>
                      </>
                    ) : (
                      <span className="text-lg font-bold text-gray-900">
                        {product.price.toLocaleString('vi-VN')}đ
                      </span>
                    )}
                  </div>
                  {product.discountPercentage > 0 && (
                    <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                      -{product.discountPercentage}%
                    </span>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    Còn lại: {product.stock}
                  </span>
                  <Link
                    to={`/products/detail/${product.slug}`}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-sm"
                  >
                    Xem chi tiết
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Products;
