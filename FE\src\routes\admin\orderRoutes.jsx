import { Route } from 'react-router-dom';
import AdminLayout from '../../layouts/AdminLayout';
import ProtectedRoute from '../ProtectedRoute';

// Import các trang quản lý đơn hàng
// import OrdersManagement from '../../pages/admin/OrdersManagement';
// import OrderDetail from '../../pages/admin/OrderDetail';

// Routes cho quản lý đơn hàng trong admin
const AdminOrderRoutes = () => {
  return (
    <Route path="/admin" element={
      <ProtectedRoute requireAuth={true} requireAdmin={true}>
        <AdminLayout />
      </ProtectedRoute>
    }>
      {/* Danh sách đơn hàng */}
      <Route 
        path="orders" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Quản lý đơn hàng</h1>
            <p className="text-gray-600">Danh sách đơn hàng đang được phát triển...</p>
          </div>
        } 
      />
      
      {/* Chi tiết đơn hàng */}
      <Route 
        path="orders/:id" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Chi tiết đơn hàng</h1>
            <p className="text-gray-600">Chi tiết đơn hàng đang được phát triển...</p>
          </div>
        } 
      />
      
      {/* Cập nhật trạng thái đơn hàng */}
      <Route 
        path="orders/:id/status" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Cập nhật trạng thái</h1>
            <p className="text-gray-600">Form cập nhật trạng thái đang được phát triển...</p>
          </div>
        } 
      />
    </Route>
  );
};

export default AdminOrderRoutes;
