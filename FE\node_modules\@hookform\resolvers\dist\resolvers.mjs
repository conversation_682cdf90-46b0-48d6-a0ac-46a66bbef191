import{get as e,set as t}from"react-hook-form";const r=(t,r,o)=>{if(t&&"reportValidity"in t){const s=e(o,r);t.setCustomValidity(s&&s.message||""),t.reportValidity()}},o=(e,t)=>{for(const o in t.fields){const s=t.fields[o];s&&s.ref&&"reportValidity"in s.ref?r(s.ref,o,e):s&&s.refs&&s.refs.forEach(t=>r(t,o,e))}},s=(r,s)=>{s.shouldUseNativeValidation&&o(r,s);const n={};for(const o in r){const f=e(s.fields,o),c=Object.assign(r[o]||{},{ref:f&&f.ref});if(i(s.names||Object.keys(r),o)){const r=Object.assign({},e(n,o));t(r,"root",c),t(n,o,r)}else t(n,o,c)}return n},i=(e,t)=>{const r=n(t);return e.some(e=>n(e).match(`^${r}\\.\\d+`))};function n(e){return e.replace(/\]|\[/g,"")}export{s as toNestErrors,o as validateFieldsNatively};
//# sourceMappingURL=resolvers.mjs.map
