var r=require("@hookform/resolvers");exports.computedTypesResolver=function(e){return function(t,o,n){try{return Promise.resolve(function(o,s){try{var u=Promise.resolve(e(t)).then(function(e){return n.shouldUseNativeValidation&&r.validateFieldsNatively({},n),{errors:{},values:e}})}catch(r){return s(r)}return u&&u.then?u.then(void 0,s):u}(0,function(e){if(function(r){return null!=r.errors}(e))return{values:{},errors:r.toNestErrors((t=e,(t.errors||[]).reduce(function(r,e){return r[e.path.join(".")]={type:e.error.name,message:e.error.message},r},{})),n)};var t;throw e}))}catch(r){return Promise.reject(r)}}};
//# sourceMappingURL=computed-types.js.map
