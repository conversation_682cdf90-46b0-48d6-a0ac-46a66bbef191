import { Route } from 'react-router-dom';
import ClientLayout from '../layouts/ClientLayout';

// Import các trang client
import Home from '../pages/client/Home';
import Products from '../pages/client/Products';
import ProductDetail from '../pages/client/ProductDetail';
import Cart from '../pages/client/Cart';
import Checkout from '../pages/client/Checkout';

// Component định nghĩa các routes cho phần client
const ClientRoutes = () => {
  return (
    <Route path="/" element={<ClientLayout />}>
      {/* Trang chủ */}
      <Route index element={<Home />} />
      
      {/* Trang sản phẩm */}
      <Route path="products" element={<Products />} />
      
      {/* Chi tiết sản phẩm */}
      <Route path="products/detail/:slug" element={<ProductDetail />} />
      
      {/* Giỏ hàng */}
      <Route path="cart" element={<Cart />} />
      
      {/* Thanh toán */}
      <Route path="checkout" element={<Checkout />} />
    </Route>
  );
};

export default ClientRoutes;
