var r=require("@hookform/resolvers"),e=require("react-hook-form");function n(r){if(r&&r.__esModule)return r;var e=Object.create(null);return r&&Object.keys(r).forEach(function(n){if("default"!==n){var o=Object.getOwnPropertyDescriptor(r,n);Object.defineProperty(e,n,o.get?o:{enumerable:!0,get:function(){return r[n]}})}}),e.default=r,e}var o=/*#__PURE__*/n(require("zod/v4/core"));function t(r,e){try{var n=r()}catch(r){return e(r)}return n&&n.then?n.then(void 0,e):n}function s(r,n){for(var o={};r.length;){var t=r[0],s=t.code,i=t.message,a=t.path.join(".");if(!o[a])if("unionErrors"in t){var u=t.unionErrors[0].errors[0];o[a]={message:u.message,type:u.code}}else o[a]={message:i,type:s};if("unionErrors"in t&&t.unionErrors.forEach(function(e){return e.errors.forEach(function(e){return r.push(e)})}),n){var c=o[a].types,f=c&&c[t.code];o[a]=e.appendErrors(a,n,o,s,f?[].concat(f,t.message):t.message)}r.shift()}return o}function i(r,n){for(var o={};r.length;){var t=r[0],s=t.code,i=t.message,a=t.path.join(".");if(!o[a])if("invalid_union"===t.code&&t.errors.length>0){var u=t.errors[0][0];o[a]={message:u.message,type:u.code}}else o[a]={message:i,type:s};if("invalid_union"===t.code&&t.errors.forEach(function(e){return e.forEach(function(e){return r.push(e)})}),n){var c=o[a].types,f=c&&c[t.code];o[a]=e.appendErrors(a,n,o,s,f?[].concat(f,t.message):t.message)}r.shift()}return o}exports.zodResolver=function(e,n,a){if(void 0===a&&(a={}),function(r){return"_def"in r&&"object"==typeof r._def&&"typeName"in r._def}(e))return function(o,i,u){try{return Promise.resolve(t(function(){return Promise.resolve(e["sync"===a.mode?"parse":"parseAsync"](o,n)).then(function(e){return u.shouldUseNativeValidation&&r.validateFieldsNatively({},u),{errors:{},values:a.raw?Object.assign({},o):e}})},function(e){if(function(r){return Array.isArray(null==r?void 0:r.issues)}(e))return{values:{},errors:r.toNestErrors(s(e.errors,!u.shouldUseNativeValidation&&"all"===u.criteriaMode),u)};throw e}))}catch(r){return Promise.reject(r)}};if(function(r){return"_zod"in r&&"object"==typeof r._zod}(e))return function(s,u,c){try{return Promise.resolve(t(function(){return Promise.resolve(("sync"===a.mode?o.parse:o.parseAsync)(e,s,n)).then(function(e){return c.shouldUseNativeValidation&&r.validateFieldsNatively({},c),{errors:{},values:a.raw?Object.assign({},s):e}})},function(e){if(function(r){return r instanceof o.$ZodError}(e))return{values:{},errors:r.toNestErrors(i(e.issues,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)};throw e}))}catch(r){return Promise.reject(r)}};throw new Error("Invalid input: not a Zod schema")};
//# sourceMappingURL=zod.js.map
