var e=require("@hookform/resolvers"),r=require("ajv"),a=require("ajv-errors"),s=require("ajv-formats"),o=require("react-hook-form");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var i=/*#__PURE__*/t(r),n=/*#__PURE__*/t(a),u=/*#__PURE__*/t(s),c=function(e,r){for(var a={},s=function(e){"required"===e.keyword&&(e.instancePath+="/"+e.params.missingProperty);var s=e.instancePath.substring(1).replace(/\//g,".");if(a[s]||(a[s]={message:e.message,type:e.keyword}),r){var t=a[s].types,i=t&&t[e.keyword];a[s]=o.appendErrors(s,r,a,e.keyword,i?[].concat(i,e.message||""):e.message)}},t=function(){var r=e[i];"errorMessage"===r.keyword?r.params.errors.forEach(function(e){e.message=r.message,s(e)}):s(r)},i=0;i<e.length;i+=1)t();return a};exports.ajvResolver=function(r,a,s){return void 0===s&&(s={}),function(o,t,l){try{var v=new i.default(Object.assign({},{allErrors:!0,validateSchema:!0},a));n.default(v),u.default(v);var d=v.compile(Object.assign({$async:s&&"async"===s.mode},r)),f=d(o);return l.shouldUseNativeValidation&&e.validateFieldsNatively({},l),Promise.resolve(f?{values:o,errors:{}}:{values:{},errors:e.toNestErrors(c(d.errors,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)})}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=ajv.js.map
