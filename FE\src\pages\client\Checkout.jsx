import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Loading from '../../components/common/Loading';
import { getCart } from '../../services/cartService';
import { createOrder, createVNPayPayment, createMomoPayment } from '../../services/orderService';

const Checkout = () => {
  const navigate = useNavigate();
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    address: '',
    paymentMethod: 'cod'
  });

  useEffect(() => {
    fetchCart();
  }, []);

  const fetchCart = async () => {
    try {
      setLoading(true);
      const response = await getCart();
      setCart(response.data.cart);
      
      if (!response.data.cart || response.data.cart.products.length === 0) {
        navigate('/cart');
      }
    } catch (err) {
      setError('Không thể tải thông tin giỏ hàng');
      console.error('Error fetching cart:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      const orderData = {
        userInfo: {
          fullName: formData.fullName,
          phone: formData.phone,
          address: formData.address,
          toadoa: {
            type: 'Point',
            coordinates: [106.6297, 10.8231] // Default coordinates for Ho Chi Minh City
          }
        },
        products: cart.products.map(item => ({
          product_id: item.product_id,
          title: item.productInfo.title,
          thumbnail: item.productInfo.thumbnail,
          price: item.productInfo.price,
          discountPercentage: item.productInfo.discountPercentage || 0,
          quantity: item.quantity
        })),
        status: 'pending'
      };

      if (formData.paymentMethod === 'cod') {
        // Cash on delivery
        const response = await createOrder(orderData);
        if (response.data.code === 200) {
          alert('Đặt hàng thành công!');
          navigate('/orders');
        }
      } else if (formData.paymentMethod === 'vnpay') {
        // VNPay payment
        const paymentData = {
          amount: cart.totalPrice,
          orderInfo: `Thanh toán đơn hàng ${Date.now()}`,
          returnUrl: `${window.location.origin}/payment/success`
        };
        const response = await createVNPayPayment(paymentData);
        if (response.data.paymentUrl) {
          window.location.href = response.data.paymentUrl;
        }
      } else if (formData.paymentMethod === 'momo') {
        // MoMo payment
        const paymentData = {
          amount: cart.totalPrice,
          orderInfo: `Thanh toán đơn hàng ${Date.now()}`,
          returnUrl: `${window.location.origin}/payment/success`
        };
        const response = await createMomoPayment(paymentData);
        if (response.data.payUrl) {
          window.location.href = response.data.payUrl;
        }
      }
    } catch (err) {
      setError('Có lỗi xảy ra khi đặt hàng');
      console.error('Checkout error:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) return <Loading text="Đang tải thông tin..." />;
  if (error) return <div className="text-center text-red-600 py-8">{error}</div>;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Thanh toán</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Checkout Form */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Thông tin giao hàng</h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                  Họ và tên *
                </label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  required
                  value={formData.fullName}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="Nhập họ và tên"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Số điện thoại *
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  required
                  value={formData.phone}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="Nhập số điện thoại"
                />
              </div>

              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  Địa chỉ giao hàng *
                </label>
                <textarea
                  id="address"
                  name="address"
                  required
                  rows={3}
                  value={formData.address}
                  onChange={handleChange}
                  className="input-field"
                  placeholder="Nhập địa chỉ chi tiết"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Phương thức thanh toán
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="cod"
                      checked={formData.paymentMethod === 'cod'}
                      onChange={handleChange}
                      className="mr-2"
                    />
                    <span>Thanh toán khi nhận hàng (COD)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="vnpay"
                      checked={formData.paymentMethod === 'vnpay'}
                      onChange={handleChange}
                      className="mr-2"
                    />
                    <span>Thanh toán VNPay</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="momo"
                      checked={formData.paymentMethod === 'momo'}
                      onChange={handleChange}
                      className="mr-2"
                    />
                    <span>Thanh toán MoMo</span>
                  </label>
                </div>
              </div>

              <button
                type="submit"
                disabled={submitting}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {submitting ? 'Đang xử lý...' : 'Đặt hàng'}
              </button>
            </form>
          </div>
        </div>

        {/* Order Summary */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Đơn hàng của bạn</h2>
            
            <div className="space-y-4">
              {cart?.products.map((item) => (
                <div key={item.product_id} className="flex items-center space-x-4 py-3 border-b border-gray-200 last:border-b-0">
                  <img
                    src={item.productInfo?.thumbnail || '/placeholder-image.jpg'}
                    alt={item.productInfo?.title}
                    className="w-16 h-16 object-cover rounded"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{item.productInfo?.title}</h4>
                    <p className="text-sm text-gray-600">Số lượng: {item.quantity}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      {item.totalPrice?.toLocaleString('vi-VN')}đ
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="border-t pt-4 mt-4 space-y-2">
              <div className="flex justify-between">
                <span>Tạm tính:</span>
                <span>{cart?.totalPrice?.toLocaleString('vi-VN')}đ</span>
              </div>
              <div className="flex justify-between">
                <span>Phí vận chuyển:</span>
                <span>Miễn phí</span>
              </div>
              <div className="flex justify-between text-lg font-bold border-t pt-2">
                <span>Tổng cộng:</span>
                <span className="text-red-600">{cart?.totalPrice?.toLocaleString('vi-VN')}đ</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
