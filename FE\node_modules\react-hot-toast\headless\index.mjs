var N=e=>typeof e=="function",D=(e,t)=>N(e)?e(t):e;var V=(()=>{let e=0;return()=>(++e).toString()})(),J=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})();import{useEffect as L,useState as C,useRef as H}from"react";var j=20,h="default";var v=(e,t)=>{let{toastLimit:s}=e.settings;switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,s)};case 1:return{...e,toasts:e.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case 2:let{toast:r}=t;return v(e,{type:e.toasts.find(o=>o.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(o=>o.id===a||a===void 0?{...o,dismissed:!0,visible:!1}:o)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(o=>o.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let T=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(o=>({...o,pauseDuration:o.pauseDuration+T}))}}},O=[],I={toasts:[],pausedAt:void 0,settings:{toastLimit:j}},l={},M=(e,t=h)=>{l[t]=v(l[t]||I,e),O.forEach(([s,r])=>{s===t&&r(l[t])})},P=e=>Object.keys(l).forEach(t=>M(e,t)),U=e=>Object.keys(l).find(t=>l[t].toasts.some(s=>s.id===e)),f=(e=h)=>t=>{M(t,e)},Q={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},x=(e={},t=h)=>{let[s,r]=C(l[t]||I),a=H(l[t]);L(()=>(a.current!==l[t]&&r(l[t]),O.push([t,r]),()=>{let o=O.findIndex(([p])=>p===t);o>-1&&O.splice(o,1)}),[t]);let T=s.toasts.map(o=>{var p,A,y;return{...e,...e[o.type],...o,removeDelay:o.removeDelay||((p=e[o.type])==null?void 0:p.removeDelay)||(e==null?void 0:e.removeDelay),duration:o.duration||((A=e[o.type])==null?void 0:A.duration)||(e==null?void 0:e.duration)||Q[o.type],style:{...e.style,...(y=e[o.type])==null?void 0:y.style,...o.style}}});return{...s,toasts:T}};var B=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(s==null?void 0:s.id)||V()}),S=e=>(t,s)=>{let r=B(t,e,s);return f(r.toasterId||U(r.id))({type:2,toast:r}),r.id},n=(e,t)=>S("blank")(e,t);n.error=S("error");n.success=S("success");n.loading=S("loading");n.custom=S("custom");n.dismiss=(e,t)=>{let s={type:3,toastId:e};t?f(t)(s):P(s)};n.dismissAll=e=>n.dismiss(void 0,e);n.remove=(e,t)=>{let s={type:4,toastId:e};t?f(t)(s):P(s)};n.removeAll=e=>n.remove(void 0,e);n.promise=(e,t,s)=>{let r=n.loading(t.loading,{...s,...s==null?void 0:s.loading});return typeof e=="function"&&(e=e()),e.then(a=>{let T=t.success?D(t.success,a):void 0;return T?n.success(T,{id:r,...s,...s==null?void 0:s.success}):n.dismiss(r),a}).catch(a=>{let T=t.error?D(t.error,a):void 0;T?n.error(T,{id:r,...s,...s==null?void 0:s.error}):n.dismiss(r)}),e};import{useEffect as F,useCallback as m,useRef as W}from"react";var X=1e3,q=(e,t="default")=>{let{toasts:s,pausedAt:r}=x(e,t),a=W(new Map).current,T=m((i,c=X)=>{if(a.has(i))return;let u=setTimeout(()=>{a.delete(i),o({type:4,toastId:i})},c);a.set(i,u)},[]);F(()=>{if(r)return;let i=Date.now(),c=s.map(u=>{if(u.duration===1/0)return;let g=(u.duration||0)+u.pauseDuration-(i-u.createdAt);if(g<0){u.visible&&n.dismiss(u.id);return}return setTimeout(()=>n.dismiss(u.id,t),g)});return()=>{c.forEach(u=>u&&clearTimeout(u))}},[s,r,t]);let o=m(f(t),[t]),p=m(()=>{o({type:5,time:Date.now()})},[o]),A=m((i,c)=>{o({type:1,toast:{id:i,height:c}})},[o]),y=m(()=>{r&&o({type:6,time:Date.now()})},[r,o]),k=m((i,c)=>{let{reverseOrder:u=!1,gutter:g=8,defaultPosition:R}=c||{},E=s.filter(d=>(d.position||R)===(i.position||R)&&d.height),w=E.findIndex(d=>d.id===i.id),_=E.filter((d,b)=>b<w&&d.visible).length;return E.filter(d=>d.visible).slice(...u?[_+1]:[0,_]).reduce((d,b)=>d+(b.height||0)+g,0)},[s]);return F(()=>{s.forEach(i=>{if(i.dismissed)T(i.id,i.removeDelay);else{let c=a.get(i.id);c&&(clearTimeout(c),a.delete(i.id))}})},[s,T]),{toasts:s,handlers:{updateHeight:A,startPause:p,endPause:y,calculateOffset:k}}};var Ae=n;export{Ae as default,D as resolveValue,n as toast,q as useToaster,x as useToasterStore};
//# sourceMappingURL=index.mjs.map