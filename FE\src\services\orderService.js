import api from './api';

// === DỊCH VỤ QUẢN LÝ ĐỚN HÀNG ===

// Tạo đơn hàng mới
export const createOrder = (orderData) => {
  return api.post('/order/post', orderData);
};

// L<PERSON>y danh sách đơn hàng của người dùng
export const getUserOrders = () => {
  return api.get('/order/view');
};

// Lấy chi tiết đơn hàng
export const getOrderDetail = (orderId) => {
  return api.get(`/order/detail/${orderId}`);
};

// Cập nhật trạng thái đơn hàng
export const updateOrderStatus = (orderId, status) => {
  return api.patch(`/order/edit/${orderId}`, { status });
};

// === DỊCH VỤ THANH TOÁN ===

// Tạo URL thanh toán VNPay
export const createVNPayPayment = (paymentData) => {
  return api.post('/checkout/create_payment_url', paymentData);
};

// Tạo URL thanh toán MoMo
export const createMomoPayment = (paymentData) => {
  return api.post('/checkout/create_payment_urlMomo', paymentData);
};

// Hoàn tiền VNPay
export const refundVNPay = (refundData) => {
  return api.post('/checkout/refundVN', refundData);
};
