import api from './api';

// Create order
export const createOrder = (orderData) => {
  return api.post('/order/post', orderData);
};

// Get user orders
export const getUserOrders = () => {
  return api.get('/order/view');
};

// Get order detail
export const getOrderDetail = (orderId) => {
  return api.get(`/order/detail/${orderId}`);
};

// Update order status
export const updateOrderStatus = (orderId, status) => {
  return api.patch(`/order/edit/${orderId}`, { status });
};

// Payment services
export const createVNPayPayment = (paymentData) => {
  return api.post('/checkout/create_payment_url', paymentData);
};

export const createMomoPayment = (paymentData) => {
  return api.post('/checkout/create_payment_urlMomo', paymentData);
};

export const refundVNPay = (refundData) => {
  return api.post('/checkout/refundVN', refundData);
};
