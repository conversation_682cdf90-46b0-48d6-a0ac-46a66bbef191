import api from './api';

// === CLIENT PRODUCT SERVICES ===

// L<PERSON><PERSON> tất cả sản phẩm cho client
export const getProducts = () => {
  return api.get('/products');
};

// L<PERSON>y chi tiết sản phẩm theo slug
export const getProductDetail = (slug) => {
  return api.get(`/products/detail/${slug}`);
};

// Lấy sản phẩm theo danh mục
export const getProductsByCategory = (categorySlug) => {
  return api.get(`/products/${categorySlug}`);
};

// Tìm kiếm sản phẩm
export const searchProducts = (query) => {
  return api.get(`/search?keyword=${encodeURIComponent(query)}`);
};

// === ADMIN PRODUCT SERVICES ===

// L<PERSON>y tất cả sản phẩm cho admin
export const getAdminProducts = () => {
  return api.get('/admin/products');
};

// Tạo sản phẩm mới
export const createProduct = (productData) => {
  return api.post('/admin/products/createPost', productData);
};

// Cập nhật sản phẩm
export const updateProduct = (productData) => {
  return api.patch('/admin/products/edit', productData);
};

// Xóa sản phẩm
export const deleteProduct = (productId) => {
  return api.patch('/admin/products/delete-item', { id: productId });
};

// Thay đổi trạng thái sản phẩm (active/inactive)
export const changeProductStatus = (productId, status) => {
  return api.patch('/admin/products/change-status', {
    id: productId,
    status
  });
};
