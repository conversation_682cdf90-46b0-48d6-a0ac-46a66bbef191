import api from './api';

// Get all products
export const getProducts = () => {
  return api.get('/products');
};

// Get product detail by slug
export const getProductDetail = (slug) => {
  return api.get(`/products/detail/${slug}`);
};

// Get products by category
export const getProductsByCategory = (categorySlug) => {
  return api.get(`/products/${categorySlug}`);
};

// Search products
export const searchProducts = (query) => {
  return api.get(`/search?keyword=${encodeURIComponent(query)}`);
};

// Admin product services
export const getAdminProducts = () => {
  return api.get('/admin/products');
};

export const createProduct = (productData) => {
  return api.post('/admin/products/createPost', productData);
};

export const updateProduct = (productData) => {
  return api.patch('/admin/products/edit', productData);
};

export const deleteProduct = (productId) => {
  return api.patch('/admin/products/delete-item', { id: productId });
};

export const changeProductStatus = (productId, status) => {
  return api.patch('/admin/products/change-status', { 
    id: productId, 
    status 
  });
};
