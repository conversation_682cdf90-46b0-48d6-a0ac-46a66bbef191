import { Route } from 'react-router-dom';
import AdminLayout from '../../layouts/AdminLayout';
import ProtectedRoute from '../ProtectedRoute';

// Import các trang quản lý sản phẩm
import ProductsManagement from '../../pages/admin/ProductsManagement';
// import ProductCreate from '../../pages/admin/ProductCreate';
// import ProductEdit from '../../pages/admin/ProductEdit';
// import ProductDetail from '../../pages/admin/ProductDetail';

// Routes cho quản lý sản phẩm trong admin
const AdminProductRoutes = () => {
  return (
    <Route path="/admin" element={
      <ProtectedRoute requireAuth={true} requireAdmin={true}>
        <AdminLayout />
      </ProtectedRoute>
    }>
      {/* Danh sách sản phẩm */}
      <Route path="products" element={<ProductsManagement />} />
      
      {/* Tạo sản phẩm mới */}
      <Route 
        path="products/create" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Thêm sản phẩm mới</h1>
            <p className="text-gray-600">Form tạo sản phẩm đang được phát triển...</p>
          </div>
        } 
      />
      
      {/* Chỉnh sửa sản phẩm */}
      <Route 
        path="products/edit/:slug" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Chỉnh sửa sản phẩm</h1>
            <p className="text-gray-600">Form chỉnh sửa sản phẩm đang được phát triển...</p>
          </div>
        } 
      />
      
      {/* Chi tiết sản phẩm trong admin */}
      <Route 
        path="products/detail/:slug" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Chi tiết sản phẩm</h1>
            <p className="text-gray-600">Trang chi tiết sản phẩm admin đang được phát triển...</p>
          </div>
        } 
      />
    </Route>
  );
};

export default AdminProductRoutes;
