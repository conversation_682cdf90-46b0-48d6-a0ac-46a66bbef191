import React, { useState, useEffect } from 'react';
import { 
  ShoppingBagIcon, 
  UsersIcon, 
  ShoppingCartIcon,
  CurrencyDollarIcon 
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalOrders: 0,
    totalUsers: 0,
    totalRevenue: 0
  });

  // Mock data - replace with actual API calls
  useEffect(() => {
    // Simulate API call
    setStats({
      totalProducts: 150,
      totalOrders: 89,
      totalUsers: 245,
      totalRevenue: 15750000
    });
  }, []);

  const statCards = [
    {
      title: 'Tổng sản phẩm',
      value: stats.totalProducts,
      icon: ShoppingBagIcon,
      color: 'bg-blue-500',
      textColor: 'text-blue-600'
    },
    {
      title: 'Đơn hàng',
      value: stats.totalOrders,
      icon: ShoppingCartIcon,
      color: 'bg-green-500',
      textColor: 'text-green-600'
    },
    {
      title: '<PERSON><PERSON><PERSON><PERSON> hàng',
      value: stats.totalUsers,
      icon: UsersIcon,
      color: 'bg-purple-500',
      textColor: 'text-purple-600'
    },
    {
      title: '<PERSON><PERSON>h thu',
      value: `${stats.totalRevenue.toLocaleString('vi-VN')}đ`,
      icon: CurrencyDollarIcon,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600'
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-600">
          Tổng quan về hoạt động của cửa hàng
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat, index) => (
          <div key={index} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className={`h-6 w-6 ${stat.textColor}`} />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.title}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Đơn hàng gần đây
            </h3>
            <div className="space-y-3">
              {[1, 2, 3, 4, 5].map((order) => (
                <div key={order} className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      Đơn hàng #{order.toString().padStart(4, '0')}
                    </p>
                    <p className="text-sm text-gray-500">
                      Khách hàng: Nguyễn Văn A
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {(Math.random() * 1000000 + 100000).toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}đ
                    </p>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Hoàn thành
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Sản phẩm bán chạy
            </h3>
            <div className="space-y-3">
              {[
                { name: 'iPhone 15 Pro Max', sold: 45 },
                { name: 'Samsung Galaxy S24', sold: 38 },
                { name: 'MacBook Air M2', sold: 32 },
                { name: 'iPad Pro 11"', sold: 28 },
                { name: 'AirPods Pro', sold: 25 }
              ].map((product, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {product.name}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      Đã bán: {product.sold}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
