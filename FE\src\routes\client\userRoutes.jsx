import { Route } from 'react-router-dom';
import ClientLayout from '../../layouts/ClientLayout';
import ProtectedRoute from '../ProtectedRoute';

// Import các trang yêu cầu đăng nhập
// import Profile from '../../pages/client/Profile';
// import Orders from '../../pages/client/Orders';
// import OrderDetail from '../../pages/client/OrderDetail';

// Routes cho người dùng đã đăng nhập
const UserRoutes = () => {
  return (
    <Route path="/" element={<ClientLayout />}>
      {/* Thông tin cá nhân - yêu cầu đăng nhập */}
      <Route 
        path="profile" 
        element={
          <ProtectedRoute requireAuth={true}>
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Thông tin cá nhân</h1>
              <p className="text-gray-600">Tính năng đang được phát triển...</p>
            </div>
          </ProtectedRoute>
        } 
      />
      
      {/* <PERSON><PERSON><PERSON> sử đơn hàng - yêu cầu đăng nhập */}
      <Route 
        path="orders" 
        element={
          <ProtectedRoute requireAuth={true}>
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Đơn hàng của tôi</h1>
              <p className="text-gray-600">Tính năng đang được phát triển...</p>
            </div>
          </ProtectedRoute>
        } 
      />
      
      {/* Chi tiết đơn hàng - yêu cầu đăng nhập */}
      <Route 
        path="orders/:id" 
        element={
          <ProtectedRoute requireAuth={true}>
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Chi tiết đơn hàng</h1>
              <p className="text-gray-600">Tính năng đang được phát triển...</p>
            </div>
          </ProtectedRoute>
        } 
      />
    </Route>
  );
};

export default UserRoutes;
