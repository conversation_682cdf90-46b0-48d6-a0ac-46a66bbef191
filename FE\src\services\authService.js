import api from './api';

// === DỊCH VỤ XÁC THỰC NGƯỜI DÙNG ===

// Đăng nhập người dùng
export const login = (credentials) => {
  return api.post('/auth/login', credentials);
};

// Đăng ký tài khoản mới
export const register = (userData) => {
  return api.post('/auth/register', userData);
};

// Đăng xuất người dùng
export const logout = () => {
  return api.post('/auth/logout');
};

// Gửi yêu cầu quên mật khẩu
export const forgotPassword = (email) => {
  return api.post('/auth/password/forgot', { email });
};

// Xác thực mã OTP
export const verifyOTP = (email, otp) => {
  return api.post('/auth/password/otp', { email, otp });
};

// Đặt lại mật khẩu mới
export const resetPassword = (email, otp, password) => {
  return api.post('/auth/password/reset', {
    email,
    otp,
    password
  });
};

// Lấy thông tin người dùng hiện tại
export const getUserInfo = () => {
  return api.get('/user/info');
};
