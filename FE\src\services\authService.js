import api from './api';

// User authentication
export const login = (credentials) => {
  return api.post('/auth/login', credentials);
};

export const register = (userData) => {
  return api.post('/auth/register', userData);
};

export const logout = () => {
  return api.post('/auth/logout');
};

export const forgotPassword = (email) => {
  return api.post('/auth/password/forgot', { email });
};

export const verifyOTP = (email, otp) => {
  return api.post('/auth/password/otp', { email, otp });
};

export const resetPassword = (email, otp, password) => {
  return api.post('/auth/password/reset', { 
    email, 
    otp, 
    password 
  });
};

// Get user info
export const getUserInfo = () => {
  return api.get('/user/info');
};
