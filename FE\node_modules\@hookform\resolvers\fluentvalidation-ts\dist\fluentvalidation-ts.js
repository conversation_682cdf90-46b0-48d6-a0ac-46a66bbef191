var e=require("@hookform/resolvers");function r(e,t,o){void 0===o&&(o=[]);var n=function(){var n=[].concat(o,[a]),i=e[a];Array.isArray(i)?i.forEach(function(e,o){r(e,t,[].concat(n,[o]))}):"object"==typeof i&&null!==i?r(i,t,n):"string"==typeof i&&(t[n.join(".")]={type:"validation",message:i})};for(var a in e)n()}function t(e,t){var o={};return r(e,o),o}exports.fluentAsyncValidationResolver=function(r){return function(o,n,a){try{return Promise.resolve(r.validateAsync(o)).then(function(r){var n=0===Object.keys(r).length;return a.shouldUseNativeValidation&&e.validateFieldsNatively({},a),n?{values:o,errors:{}}:{values:{},errors:e.toNestErrors(t(r),a)}})}catch(e){return Promise.reject(e)}}},exports.fluentValidationResolver=function(r){return function(o,n,a){try{var i=r.validate(o),s=0===Object.keys(i).length;return a.shouldUseNativeValidation&&e.validateFieldsNatively({},a),Promise.resolve(s?{values:o,errors:{}}:{values:{},errors:e.toNestErrors(t(i),a)})}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=fluentvalidation-ts.js.map
