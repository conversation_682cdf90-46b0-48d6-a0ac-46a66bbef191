// <PERSON><PERSON><PERSON> hình tất cả các routes của ứng dụng
export const ROUTE_PATHS = {
  // Client routes
  HOME: '/',
  PRODUCTS: '/products',
  PRODUCT_DETAIL: '/products/detail/:slug',
  CART: '/cart',
  CHECKOUT: '/checkout',
  
  // Auth routes
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  
  // User routes (yêu cầu đăng nhập)
  PROFILE: '/profile',
  ORDERS: '/orders',
  ORDER_DETAIL: '/orders/:id',
  
  // Admin routes (yêu cầu quyền admin)
  ADMIN_DASHBOARD: '/admin',
  ADMIN_PRODUCTS: '/admin/products',
  ADMIN_PRODUCT_CREATE: '/admin/products/create',
  ADMIN_PRODUCT_EDIT: '/admin/products/edit/:slug',
  ADMIN_PRODUCT_DETAIL: '/admin/products/detail/:slug',
  ADMIN_CATEGORIES: '/admin/categories',
  ADMIN_ORDERS: '/admin/orders',
  ADMIN_USERS: '/admin/users',
  ADMIN_REPORTS: '/admin/reports',
};

// Metadata cho các routes
export const ROUTE_META = {
  [ROUTE_PATHS.HOME]: {
    title: 'Trang chủ',
    description: 'Trang chủ E-Commerce',
    requireAuth: false,
  },
  [ROUTE_PATHS.PRODUCTS]: {
    title: 'Sản phẩm',
    description: 'Danh sách sản phẩm',
    requireAuth: false,
  },
  [ROUTE_PATHS.CART]: {
    title: 'Giỏ hàng',
    description: 'Giỏ hàng của bạn',
    requireAuth: false,
  },
  [ROUTE_PATHS.CHECKOUT]: {
    title: 'Thanh toán',
    description: 'Thanh toán đơn hàng',
    requireAuth: true,
  },
  [ROUTE_PATHS.LOGIN]: {
    title: 'Đăng nhập',
    description: 'Đăng nhập tài khoản',
    requireAuth: false,
    hideWhenAuthenticated: true,
  },
  [ROUTE_PATHS.REGISTER]: {
    title: 'Đăng ký',
    description: 'Tạo tài khoản mới',
    requireAuth: false,
    hideWhenAuthenticated: true,
  },
  [ROUTE_PATHS.PROFILE]: {
    title: 'Thông tin cá nhân',
    description: 'Quản lý thông tin cá nhân',
    requireAuth: true,
  },
  [ROUTE_PATHS.ORDERS]: {
    title: 'Đơn hàng của tôi',
    description: 'Lịch sử đơn hàng',
    requireAuth: true,
  },
  [ROUTE_PATHS.ADMIN_DASHBOARD]: {
    title: 'Admin Dashboard',
    description: 'Trang quản trị',
    requireAuth: true,
    requireAdmin: true,
  },
  [ROUTE_PATHS.ADMIN_PRODUCTS]: {
    title: 'Quản lý sản phẩm',
    description: 'Quản lý sản phẩm',
    requireAuth: true,
    requireAdmin: true,
  },
};
