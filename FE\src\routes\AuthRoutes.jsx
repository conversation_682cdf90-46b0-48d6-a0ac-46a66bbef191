import { Route } from 'react-router-dom';

// Import các trang xác thực
import Login from '../pages/client/Login';
import Register from '../pages/client/Register';

// Component định ngh<PERSON>a các routes cho xác thực (không có layout)
const AuthRoutes = () => {
  return (
    <>
      {/* Trang đăng nhập */}
      <Route path="/auth/login" element={<Login />} />
      
      {/* Trang đăng ký */}
      <Route path="/auth/register" element={<Register />} />
      
      {/* C<PERSON> thể thêm các routes khác như quên mật khẩu */}
      {/* <Route path="/auth/forgot-password" element={<ForgotPassword />} /> */}
      {/* <Route path="/auth/reset-password" element={<ResetPassword />} /> */}
    </>
  );
};

export default AuthRoutes;
