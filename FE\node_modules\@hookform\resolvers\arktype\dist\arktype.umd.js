!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers"],r):r((e||self).hookformResolversArktype={},e.hookformResolvers)}(this,function(e,r){function t(){return t=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var o in t)({}).hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},t.apply(null,arguments)}function o(e){if(e.path?.length){let r="";for(const t of e.path){const e="object"==typeof t?t.key:t;if("string"!=typeof e&&"number"!=typeof e)return null;r+=r?`.${e}`:e}return r}return null}e.arktypeResolver=function(e,n,s){return void 0===s&&(s={}),function(n,i,a){try{var f=function(){if(l.issues){var e=function(e,r){for(var n={},s=0;s<e.length;s++){var i=e[s],a=o(i);if(a&&(n[a]||(n[a]={message:i.message,type:""}),r)){var f,l=n[a].types||{};n[a].types=t({},l,((f={})[Object.keys(l).length]=i.message,f))}}return n}(l.issues,!a.shouldUseNativeValidation&&"all"===a.criteriaMode);return{values:{},errors:r.toNestErrors(e,a)}}return a.shouldUseNativeValidation&&r.validateFieldsNatively({},a),{values:s.raw?Object.assign({},n):l.value,errors:{}}},l=e["~standard"].validate(n),u=function(){if(l instanceof Promise)return Promise.resolve(l).then(function(e){l=e})}();return Promise.resolve(u&&u.then?u.then(f):f())}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=arktype.umd.js.map
