import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Loading from '../../components/common/Loading';
import { getProducts } from '../../services/productService';

const Home = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await getProducts();
        setProducts(response.data.products || []);
      } catch (err) {
        setError('Không thể tải sản phẩm');
        console.error('Error fetching products:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) return <Loading text="Đang tải sản phẩm..." />;
  if (error) return <div className="text-center text-red-600 py-8">{error}</div>;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 mb-12">
        <div className="max-w-2xl">
          <h1 className="text-4xl font-bold mb-4">
            Chào mừng đến với E-Commerce
          </h1>
          <p className="text-xl mb-6">
            Khám phá hàng ngàn sản phẩm chất lượng cao với giá tốt nhất
          </p>
          <Link 
            to="/products" 
            className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Mua sắm ngay
          </Link>
        </div>
      </section>

      {/* Featured Products */}
      <section>
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Sản phẩm nổi bật</h2>
        
        {products.length === 0 ? (
          <div className="text-center text-gray-600 py-8">
            Không có sản phẩm nào
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {products.slice(0, 8).map((product) => (
              <div key={product._id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="aspect-w-1 aspect-h-1">
                  <img
                    src={product.thumbnail || '/placeholder-image.jpg'}
                    alt={product.title}
                    className="w-full h-48 object-cover"
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {product.title}
                  </h3>
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      {product.discountPercentage > 0 ? (
                        <>
                          <span className="text-lg font-bold text-red-600">
                            {(product.price * (100 - product.discountPercentage) / 100).toLocaleString('vi-VN')}đ
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            {product.price.toLocaleString('vi-VN')}đ
                          </span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900">
                          {product.price.toLocaleString('vi-VN')}đ
                        </span>
                      )}
                    </div>
                    {product.discountPercentage > 0 && (
                      <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                        -{product.discountPercentage}%
                      </span>
                    )}
                  </div>
                  <Link
                    to={`/products/detail/${product.slug}`}
                    className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors text-center block"
                  >
                    Xem chi tiết
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {products.length > 8 && (
          <div className="text-center mt-8">
            <Link
              to="/products"
              className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
            >
              Xem tất cả sản phẩm
            </Link>
          </div>
        )}
      </section>
    </div>
  );
};

export default Home;
