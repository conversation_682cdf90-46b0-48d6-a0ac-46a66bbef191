"use strict";var x=Object.defineProperty;var C=Object.getOwnPropertyDescriptor;var H=Object.getOwnPropertyNames;var j=Object.prototype.hasOwnProperty;var Q=(e,t)=>{for(var s in t)x(e,s,{get:t[s],enumerable:!0})},Y=(e,t,s,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of H(t))!j.call(e,n)&&n!==s&&x(e,n,{get:()=>t[n],enumerable:!(r=C(t,n))||r.enumerable});return e};var B=e=>Y(x({},"__esModule",{value:!0}),e);var Z={};Q(Z,{default:()=>K,resolveValue:()=>A,toast:()=>i,useToaster:()=>w,useToasterStore:()=>b});module.exports=B(Z);var W=e=>typeof e=="function",A=(e,t)=>W(e)?e(t):e;var I=(()=>{let e=0;return()=>(++e).toString()})(),se=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})();var m=require("react");var X=20,R="default";var M=(e,t)=>{let{toastLimit:s}=e.settings;switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,s)};case 1:return{...e,toasts:e.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case 2:let{toast:r}=t;return M(e,{type:e.toasts.find(o=>o.id===r.id)?1:0,toast:r});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map(o=>o.id===n||n===void 0?{...o,dismissed:!0,visible:!1}:o)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(o=>o.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let T=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(o=>({...o,pauseDuration:o.pauseDuration+T}))}}},E=[],U={toasts:[],pausedAt:void 0,settings:{toastLimit:X}},p={},F=(e,t=R)=>{p[t]=M(p[t]||U,e),E.forEach(([s,r])=>{s===t&&r(p[t])})},_=e=>Object.keys(p).forEach(t=>F(e,t)),k=e=>Object.keys(p).find(t=>p[t].toasts.some(s=>s.id===e)),S=(e=R)=>t=>{F(t,e)},q={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},b=(e={},t=R)=>{let[s,r]=(0,m.useState)(p[t]||U),n=(0,m.useRef)(p[t]);(0,m.useEffect)(()=>(n.current!==p[t]&&r(p[t]),E.push([t,r]),()=>{let o=E.findIndex(([f])=>f===t);o>-1&&E.splice(o,1)}),[t]);let T=s.toasts.map(o=>{var f,g,D;return{...e,...e[o.type],...o,removeDelay:o.removeDelay||((f=e[o.type])==null?void 0:f.removeDelay)||(e==null?void 0:e.removeDelay),duration:o.duration||((g=e[o.type])==null?void 0:g.duration)||(e==null?void 0:e.duration)||q[o.type],style:{...e.style,...(D=e[o.type])==null?void 0:D.style,...o.style}}});return{...s,toasts:T}};var G=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(s==null?void 0:s.id)||I()}),y=e=>(t,s)=>{let r=G(t,e,s);return S(r.toasterId||k(r.id))({type:2,toast:r}),r.id},i=(e,t)=>y("blank")(e,t);i.error=y("error");i.success=y("success");i.loading=y("loading");i.custom=y("custom");i.dismiss=(e,t)=>{let s={type:3,toastId:e};t?S(t)(s):_(s)};i.dismissAll=e=>i.dismiss(void 0,e);i.remove=(e,t)=>{let s={type:4,toastId:e};t?S(t)(s):_(s)};i.removeAll=e=>i.remove(void 0,e);i.promise=(e,t,s)=>{let r=i.loading(t.loading,{...s,...s==null?void 0:s.loading});return typeof e=="function"&&(e=e()),e.then(n=>{let T=t.success?A(t.success,n):void 0;return T?i.success(T,{id:r,...s,...s==null?void 0:s.success}):i.dismiss(r),n}).catch(n=>{let T=t.error?A(t.error,n):void 0;T?i.error(T,{id:r,...s,...s==null?void 0:s.error}):i.dismiss(r)}),e};var c=require("react");var J=1e3,w=(e,t="default")=>{let{toasts:s,pausedAt:r}=b(e,t),n=(0,c.useRef)(new Map).current,T=(0,c.useCallback)((a,d=J)=>{if(n.has(a))return;let u=setTimeout(()=>{n.delete(a),o({type:4,toastId:a})},d);n.set(a,u)},[]);(0,c.useEffect)(()=>{if(r)return;let a=Date.now(),d=s.map(u=>{if(u.duration===1/0)return;let O=(u.duration||0)+u.pauseDuration-(a-u.createdAt);if(O<0){u.visible&&i.dismiss(u.id);return}return setTimeout(()=>i.dismiss(u.id,t),O)});return()=>{d.forEach(u=>u&&clearTimeout(u))}},[s,r,t]);let o=(0,c.useCallback)(S(t),[t]),f=(0,c.useCallback)(()=>{o({type:5,time:Date.now()})},[o]),g=(0,c.useCallback)((a,d)=>{o({type:1,toast:{id:a,height:d}})},[o]),D=(0,c.useCallback)(()=>{r&&o({type:6,time:Date.now()})},[r,o]),N=(0,c.useCallback)((a,d)=>{let{reverseOrder:u=!1,gutter:O=8,defaultPosition:V}=d||{},h=s.filter(l=>(l.position||V)===(a.position||V)&&l.height),L=h.findIndex(l=>l.id===a.id),v=h.filter((l,P)=>P<L&&l.visible).length;return h.filter(l=>l.visible).slice(...u?[v+1]:[0,v]).reduce((l,P)=>l+(P.height||0)+O,0)},[s]);return(0,c.useEffect)(()=>{s.forEach(a=>{if(a.dismissed)T(a.id,a.removeDelay);else{let d=n.get(a.id);d&&(clearTimeout(d),n.delete(a.id))}})},[s,T]),{toasts:s,handlers:{updateHeight:g,startPause:f,endPause:D,calculateOffset:N}}};var K=i;0&&(module.exports={resolveValue,toast,useToaster,useToasterStore});
//# sourceMappingURL=index.js.map