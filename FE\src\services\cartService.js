import api from './api';

// Get cart
export const getCart = () => {
  return api.get('/cart');
};

// Add product to cart
export const addToCart = (productId, quantity = 1) => {
  return api.post('/cart/add', {
    product_id: productId,
    quantity
  });
};

// Update cart item quantity
export const updateCartQuantity = (productId, quantity) => {
  return api.patch('/cart/update-quantity', {
    product_id: productId,
    quantity
  });
};

// Remove items from cart
export const removeFromCart = (productIds) => {
  return api.delete('/cart/delete', {
    data: { productIds }
  });
};
