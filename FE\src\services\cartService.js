import api from './api';

// Lấy thông tin giỏ hàng của người dùng
export const getCart = () => {
  return api.get('/cart');
};

// Thêm sản phẩm vào giỏ hàng
export const addToCart = (productId, quantity = 1) => {
  return api.post('/cart/add', {
    product_id: productId,
    quantity
  });
};

// Cập nhật số lượng sản phẩm trong giỏ hàng
export const updateCartQuantity = (productId, quantity) => {
  return api.patch('/cart/update-quantity', {
    product_id: productId,
    quantity
  });
};

// Xóa sản phẩm khỏi giỏ hàng
export const removeFromCart = (productIds) => {
  return api.delete('/cart/delete', {
    data: { productIds }
  });
};
