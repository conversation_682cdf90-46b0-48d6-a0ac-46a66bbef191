import { configureStore } from '@reduxjs/toolkit';
// Import các slice để quản lý state
import authSlice from './slices/authSlice';
import cartSlice from './slices/cartSlice';
import productSlice from './slices/productSlice';

// Cấu hình Redux store chính cho ứng dụng
export const store = configureStore({
  reducer: {
    auth: authSlice,        // Quản lý trạng thái đăng nhập/người dùng
    cart: cartSlice,        // Quản lý trạng thái giỏ hàng
    products: productSlice, // Quản lý trạng thái sản phẩm
  },
  // Cấu hình middleware
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Bỏ qua các action không serialize được
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

// Export store để sử dụng trong ứng dụng
// <PERSON><PERSON> thể thêm types cho TypeScript sau này nếu cần
