import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';

// Component bảo vệ routes yêu cầu đăng nhập
const ProtectedRoute = ({ children, requireAuth = true, requireAdmin = false }) => {
  const location = useLocation();
  const { isAuthenticated, user } = useSelector(state => state.auth);

  // Nếu yêu cầu đăng nhập nhưng chưa đăng nhập
  if (requireAuth && !isAuthenticated) {
    // Lưu đường dẫn hiện tại để redirect sau khi đăng nhập
    return <Navigate to="/auth/login" state={{ from: location }} replace />;
  }

  // Nếu yêu cầu quyền admin nhưng không phải admin
  if (requireAdmin && (!user || user.role !== 'admin')) {
    return <Navigate to="/" replace />;
  }

  // Nếu không yêu cầu đăng nhập nhưng đã đăng nhập (ví dụ: trang login)
  if (!requireAuth && isAuthenticated) {
    // Redirect về trang chủ hoặc trang trước đó
    const from = location.state?.from?.pathname || '/';
    return <Navigate to={from} replace />;
  }

  return children;
};

export default ProtectedRoute;
