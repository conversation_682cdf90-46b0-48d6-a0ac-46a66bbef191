import { useSelector, useDispatch } from 'react-redux';
import { useEffect } from 'react';
import { fetchUserInfo, logout } from '../store/slices/authSlice';

export const useAuth = () => {
  const dispatch = useDispatch();
  const { user, token, isAuthenticated, loading, error } = useSelector(state => state.auth);

  useEffect(() => {
    if (token && !user) {
      dispatch(fetchUserInfo());
    }
  }, [token, user, dispatch]);

  const handleLogout = () => {
    dispatch(logout());
  };

  return {
    user,
    token,
    isAuthenticated,
    loading,
    error,
    logout: handleLogout,
  };
};
