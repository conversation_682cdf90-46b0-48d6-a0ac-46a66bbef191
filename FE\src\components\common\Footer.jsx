import React from 'react';

const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">E-Commerce</h3>
            <p className="text-gray-400">
              Cửa hàng trực tuyến uy tín với nhiều sản phẩm chất lượng cao.
            </p>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Li<PERSON>n kết nhanh</h4>
            <ul className="space-y-2">
              <li><a href="/" className="text-gray-400 hover:text-white">Trang chủ</a></li>
              <li><a href="/products" className="text-gray-400 hover:text-white"><PERSON><PERSON><PERSON> phẩm</a></li>
              <li><a href="/about" className="text-gray-400 hover:text-white">V<PERSON> chúng tôi</a></li>
              <li><a href="/contact" className="text-gray-400 hover:text-white">Liên hệ</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Hỗ trợ</h4>
            <ul className="space-y-2">
              <li><a href="/help" className="text-gray-400 hover:text-white">Trợ giúp</a></li>
              <li><a href="/shipping" className="text-gray-400 hover:text-white">Vận chuyển</a></li>
              <li><a href="/returns" className="text-gray-400 hover:text-white">Đổi trả</a></li>
              <li><a href="/faq" className="text-gray-400 hover:text-white">FAQ</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Liên hệ</h4>
            <div className="text-gray-400 space-y-2">
              <p>Email: <EMAIL></p>
              <p>Phone: +84 123 456 789</p>
              <p>Địa chỉ: 123 Đường ABC, TP.HCM</p>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 E-Commerce. Tất cả quyền được bảo lưu.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
