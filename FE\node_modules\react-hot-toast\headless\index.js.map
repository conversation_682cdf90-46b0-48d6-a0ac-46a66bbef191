{"version": 3, "sources": ["../src/headless/index.ts", "../src/core/types.ts", "../src/core/utils.ts", "../src/core/store.ts", "../src/core/toast.ts", "../src/core/use-toaster.ts"], "sourcesContent": ["import { toast } from '../core/toast';\n\nexport type {\n  DefaultToastOptions,\n  IconTheme,\n  Renderable,\n  Toast,\n  ToasterProps,\n  ToastOptions,\n  ToastPosition,\n  ToastType,\n  ValueFunction,\n  ValueOrFunction,\n} from '../core/types';\n\nexport { resolveValue } from '../core/types';\nexport { useToaster } from '../core/use-toaster';\nexport { useStore as useToasterStore } from '../core/store';\n\nexport { toast };\nexport default toast;\n", "import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  toasterId?: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'toasterId'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  toasterId?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nexport const TOAST_EXPIRE_DISMISS_DELAY = 1000;\nexport const TOAST_LIMIT = 20;\nexport const DEFAULT_TOASTER_ID = 'default';\n\ninterface ToasterSettings {\n  toastLimit: number;\n}\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\nexport type Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface ToasterState {\n  toasts: Toast[];\n  settings: ToasterSettings;\n  pausedAt: number | undefined;\n}\n\ninterface State {\n  [toasterId: string]: ToasterState;\n}\n\nexport const reducer = (state: ToasterState, action: Action): ToasterState => {\n  const { toastLimit } = state.settings;\n\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, toastLimit),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<\n  [toasterId: string, reducer: (state: ToasterState) => void]\n> = [];\n\nconst defaultToasterState: ToasterState = {\n  toasts: [],\n  pausedAt: undefined,\n  settings: {\n    toastLimit: TOAST_LIMIT,\n  },\n};\nlet memoryState: State = {};\n\nexport const dispatch = (action: Action, toasterId = DEFAULT_TOASTER_ID) => {\n  memoryState[toasterId] = reducer(\n    memoryState[toasterId] || defaultToasterState,\n    action\n  );\n  listeners.forEach(([id, listener]) => {\n    if (id === toasterId) {\n      listener(memoryState[toasterId]);\n    }\n  });\n};\n\nexport const dispatchAll = (action: Action) =>\n  Object.keys(memoryState).forEach((toasterId) => dispatch(action, toasterId));\n\nexport const getToasterIdFromToastId = (toastId: string) =>\n  Object.keys(memoryState).find((toasterId) =>\n    memoryState[toasterId].toasts.some((t) => t.id === toastId)\n  );\n\nexport const createDispatch =\n  (toasterId = DEFAULT_TOASTER_ID) =>\n  (action: Action) => {\n    dispatch(action, toasterId);\n  };\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (\n  toastOptions: DefaultToastOptions = {},\n  toasterId: string = DEFAULT_TOASTER_ID\n): ToasterState => {\n  const [state, setState] = useState<ToasterState>(\n    memoryState[toasterId] || defaultToasterState\n  );\n  const initial = useRef(memoryState[toasterId]);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState[toasterId]) {\n      setState(memoryState[toasterId]);\n    }\n    listeners.push([toasterId, setState]);\n    return () => {\n      const index = listeners.findIndex(([id]) => id === toasterId);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [toasterId]);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport {\n  createDispatch,\n  Action,\n  ActionType,\n  dispatchAll,\n  getToasterIdFromToastId,\n} from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n\n    const dispatch = createDispatch(\n      toast.toasterId || getToasterIdFromToastId(toast.id)\n    );\n\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\n/**\n * Dismisses the toast with the given id. If no id is given, dismisses all toasts.\n * The toast will transition out and then be removed from the DOM.\n * Applies to all toasters, except when a `toasterId` is given.\n */\ntoast.dismiss = (toastId?: string, toasterId?: string) => {\n  const action: Action = {\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  };\n\n  if (toasterId) {\n    createDispatch(toasterId)(action);\n  } else {\n    dispatchAll(action);\n  }\n};\n\n/**\n * Dismisses all toasts.\n */\ntoast.dismissAll = (toasterId?: string) => toast.dismiss(undefined, toasterId);\n\n/**\n * Removes the toast with the given id.\n * The toast will be removed from the DOM without any transition.\n */\ntoast.remove = (toastId?: string, toasterId?: string) => {\n  const action: Action = {\n    type: ActionType.REMOVE_TOAST,\n    toastId,\n  };\n  if (toasterId) {\n    createDispatch(toasterId)(action);\n  } else {\n    dispatchAll(action);\n  }\n};\n\n/**\n * Removes all toasts.\n */\ntoast.removeAll = (toasterId?: string) => toast.remove(undefined, toasterId);\n\n/**\n * Create a loading toast that will automatically updates with the promise.\n */\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback, useRef } from 'react';\nimport { createDispatch, ActionType, useStore, dispatch } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nexport const REMOVE_DELAY = 1000;\n\nexport const useToaster = (\n  toastOptions?: DefaultToastOptions,\n  toasterId: string = 'default'\n) => {\n  const { toasts, pausedAt } = useStore(toastOptions, toasterId);\n  const toastTimeouts = useRef(\n    new Map<Toast['id'], ReturnType<typeof setTimeout>>()\n  ).current;\n\n  const addToRemoveQueue = useCallback(\n    (toastId: string, removeDelay = REMOVE_DELAY) => {\n      if (toastTimeouts.has(toastId)) {\n        return;\n      }\n\n      const timeout = setTimeout(() => {\n        toastTimeouts.delete(toastId);\n        dispatch({\n          type: ActionType.REMOVE_TOAST,\n          toastId: toastId,\n        });\n      }, removeDelay);\n\n      toastTimeouts.set(toastId, timeout);\n    },\n    []\n  );\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id, toasterId), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt, toasterId]);\n\n  const dispatch = useCallback(createDispatch(toasterId), [toasterId]);\n\n  const startPause = useCallback(() => {\n    dispatch({\n      type: ActionType.START_PAUSE,\n      time: Date.now(),\n    });\n  }, [dispatch]);\n\n  const updateHeight = useCallback(\n    (toastId: string, height: number) => {\n      dispatch({\n        type: ActionType.UPDATE_TOAST,\n        toast: { id: toastId, height },\n      });\n    },\n    [dispatch]\n  );\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt, dispatch]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  // Keep track of dismissed toasts and remove them after the delay\n  useEffect(() => {\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts, addToRemoveQueue]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n"], "mappings": "yaAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,EAAA,iBAAAC,EAAA,UAAAC,EAAA,eAAAC,EAAA,oBAAAC,IAAA,eAAAC,EAAAP,GCuBA,IAAMQ,EACJC,GAEA,OAAOA,GAAkB,WAEdC,EAAe,CAC1BD,EACAE,IACYH,EAAWC,CAAa,EAAIA,EAAcE,CAAG,EAAIF,EC/BxD,IAAMG,GAAS,IAAM,CAC1B,IAAIC,EAAQ,EACZ,MAAO,KACG,EAAEA,GAAO,SAAS,CAE9B,GAAG,EAEUC,IAAwB,IAAM,CAEzC,IAAIC,EAEJ,MAAO,IAAM,CACX,GAAIA,IAAuB,QAAa,OAAO,OAAW,IAAa,CACrE,IAAMC,EAAa,WAAW,kCAAkC,EAChED,EAAqB,CAACC,GAAcA,EAAW,QAEjD,OAAOD,CACT,CACF,GAAG,EClBH,IAAAE,EAA4C,iBAIrC,IAAMC,EAAc,GACdC,EAAqB,UAwD3B,IAAMC,EAAU,CAACC,EAAqBC,IAAiC,CAC5E,GAAM,CAAE,WAAAC,CAAW,EAAIF,EAAM,SAE7B,OAAQC,EAAO,KAAM,CACnB,IAAK,GACH,MAAO,CACL,GAAGD,EACH,OAAQ,CAACC,EAAO,MAAO,GAAGD,EAAM,MAAM,EAAE,MAAM,EAAGE,CAAU,CAC7D,EAEF,IAAK,GACH,MAAO,CACL,GAAGF,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOF,EAAO,MAAM,GAAK,CAAE,GAAGE,EAAG,GAAGF,EAAO,KAAM,EAAIE,CACzD,CACF,EAEF,IAAK,GACH,GAAM,CAAE,MAAAC,CAAM,EAAIH,EAClB,OAAOF,EAAQC,EAAO,CACpB,KAAMA,EAAM,OAAO,KAAMG,GAAMA,EAAE,KAAOC,EAAM,EAAE,EAC5C,EACA,EACJ,MAAAA,CACF,CAAC,EAEH,IAAK,GACH,GAAM,CAAE,QAAAC,CAAQ,EAAIJ,EAEpB,MAAO,CACL,GAAGD,EACH,OAAQA,EAAM,OAAO,IAAKG,GACxBA,EAAE,KAAOE,GAAWA,IAAY,OAC5B,CACE,GAAGF,EACH,UAAW,GACX,QAAS,EACX,EACAA,CACN,CACF,EACF,IAAK,GACH,OAAIF,EAAO,UAAY,OACd,CACL,GAAGD,EACH,OAAQ,CAAC,CACX,EAEK,CACL,GAAGA,EACH,OAAQA,EAAM,OAAO,OAAQG,GAAMA,EAAE,KAAOF,EAAO,OAAO,CAC5D,EAEF,IAAK,GACH,MAAO,CACL,GAAGD,EACH,SAAUC,EAAO,IACnB,EAEF,IAAK,GACH,IAAMK,EAAOL,EAAO,MAAQD,EAAM,UAAY,GAE9C,MAAO,CACL,GAAGA,EACH,SAAU,OACV,OAAQA,EAAM,OAAO,IAAKG,IAAO,CAC/B,GAAGA,EACH,cAAeA,EAAE,cAAgBG,CACnC,EAAE,CACJ,CACJ,CACF,EAEMC,EAEF,CAAC,EAECC,EAAoC,CACxC,OAAQ,CAAC,EACT,SAAU,OACV,SAAU,CACR,WAAYC,CACd,CACF,EACIC,EAAqB,CAAC,EAEbC,EAAW,CAACV,EAAgBW,EAAYC,IAAuB,CAC1EH,EAAYE,CAAS,EAAIb,EACvBW,EAAYE,CAAS,GAAKJ,EAC1BP,CACF,EACAM,EAAU,QAAQ,CAAC,CAACO,EAAIC,CAAQ,IAAM,CAChCD,IAAOF,GACTG,EAASL,EAAYE,CAAS,CAAC,CAEnC,CAAC,CACH,EAEaI,EAAef,GAC1B,OAAO,KAAKS,CAAW,EAAE,QAASE,GAAcD,EAASV,EAAQW,CAAS,CAAC,EAEhEK,EAA2BZ,GACtC,OAAO,KAAKK,CAAW,EAAE,KAAME,GAC7BF,EAAYE,CAAS,EAAE,OAAO,KAAMT,GAAMA,EAAE,KAAOE,CAAO,CAC5D,EAEWa,EACX,CAACN,EAAYC,IACZZ,GAAmB,CAClBU,EAASV,EAAQW,CAAS,CAC5B,EAEWO,EAET,CACF,MAAO,IACP,MAAO,IACP,QAAS,IACT,QAAS,IACT,OAAQ,GACV,EAEaC,EAAW,CACtBC,EAAoC,CAAC,EACrCT,EAAoBC,IACH,CACjB,GAAM,CAACb,EAAOsB,CAAQ,KAAI,YACxBZ,EAAYE,CAAS,GAAKJ,CAC5B,EACMe,KAAU,UAAOb,EAAYE,CAAS,CAAC,KAG7C,aAAU,KACJW,EAAQ,UAAYb,EAAYE,CAAS,GAC3CU,EAASZ,EAAYE,CAAS,CAAC,EAEjCL,EAAU,KAAK,CAACK,EAAWU,CAAQ,CAAC,EAC7B,IAAM,CACX,IAAME,EAAQjB,EAAU,UAAU,CAAC,CAACO,CAAE,IAAMA,IAAOF,CAAS,EACxDY,EAAQ,IACVjB,EAAU,OAAOiB,EAAO,CAAC,CAE7B,GACC,CAACZ,CAAS,CAAC,EAEd,IAAMa,EAAezB,EAAM,OAAO,IAAKG,GAAG,CA/M5C,IAAAuB,EAAAC,EAAAC,EA+MgD,OAC5C,GAAGP,EACH,GAAGA,EAAalB,EAAE,IAAI,EACtB,GAAGA,EACH,YACEA,EAAE,eACFuB,EAAAL,EAAalB,EAAE,IAAI,IAAnB,YAAAuB,EAAsB,eACtBL,GAAA,YAAAA,EAAc,aAChB,SACElB,EAAE,YACFwB,EAAAN,EAAalB,EAAE,IAAI,IAAnB,YAAAwB,EAAsB,YACtBN,GAAA,YAAAA,EAAc,WACdF,EAAgBhB,EAAE,IAAI,EACxB,MAAO,CACL,GAAGkB,EAAa,MAChB,IAAGO,EAAAP,EAAalB,EAAE,IAAI,IAAnB,YAAAyB,EAAsB,MACzB,GAAGzB,EAAE,KACP,CACF,EAAE,EAEF,MAAO,CACL,GAAGH,EACH,OAAQyB,CACV,CACF,ECjNA,IAAMI,EAAc,CAClBC,EACAC,EAAkB,QAClBC,KACW,CACX,UAAW,KAAK,IAAI,EACpB,QAAS,GACT,UAAW,GACX,KAAAD,EACA,UAAW,CACT,KAAM,SACN,YAAa,QACf,EACA,QAAAD,EACA,cAAe,EACf,GAAGE,EACH,IAAIA,GAAA,YAAAA,EAAM,KAAMC,EAAM,CACxB,GAEMC,EACHH,GACD,CAACD,EAASK,IAAY,CACpB,IAAMC,EAAQP,EAAYC,EAASC,EAAMI,CAAO,EAMhD,OAJiBE,EACfD,EAAM,WAAaE,EAAwBF,EAAM,EAAE,CACrD,EAES,CAAE,OAA+B,MAAAA,CAAM,CAAC,EAC1CA,EAAM,EACf,EAEIA,EAAQ,CAACN,EAAkBE,IAC/BE,EAAc,OAAO,EAAEJ,EAASE,CAAI,EAEtCI,EAAM,MAAQF,EAAc,OAAO,EACnCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,QAAUF,EAAc,SAAS,EACvCE,EAAM,OAASF,EAAc,QAAQ,EAOrCE,EAAM,QAAU,CAACG,EAAkBC,IAAuB,CACxD,IAAMC,EAAiB,CACrB,OACA,QAAAF,CACF,EAEIC,EACFH,EAAeG,CAAS,EAAEC,CAAM,EAEhCC,EAAYD,CAAM,CAEtB,EAKAL,EAAM,WAAcI,GAAuBJ,EAAM,QAAQ,OAAWI,CAAS,EAM7EJ,EAAM,OAAS,CAACG,EAAkBC,IAAuB,CACvD,IAAMC,EAAiB,CACrB,OACA,QAAAF,CACF,EACIC,EACFH,EAAeG,CAAS,EAAEC,CAAM,EAEhCC,EAAYD,CAAM,CAEtB,EAKAL,EAAM,UAAaI,GAAuBJ,EAAM,OAAO,OAAWI,CAAS,EAK3EJ,EAAM,QAAU,CACdO,EACAC,EAKAZ,IACG,CACH,IAAMa,EAAKT,EAAM,QAAQQ,EAAK,QAAS,CAAE,GAAGZ,EAAM,GAAGA,GAAA,YAAAA,EAAM,OAAQ,CAAC,EAEpE,OAAI,OAAOW,GAAY,aACrBA,EAAUA,EAAQ,GAGpBA,EACG,KAAMG,GAAM,CACX,IAAMC,EAAiBH,EAAK,QACxBI,EAAaJ,EAAK,QAASE,CAAC,EAC5B,OAEJ,OAAIC,EACFX,EAAM,QAAQW,EAAgB,CAC5B,GAAAF,EACA,GAAGb,EACH,GAAGA,GAAA,YAAAA,EAAM,OACX,CAAC,EAEDI,EAAM,QAAQS,CAAE,EAEXC,CACT,CAAC,EACA,MAAOG,GAAM,CACZ,IAAMC,EAAeN,EAAK,MAAQI,EAAaJ,EAAK,MAAOK,CAAC,EAAI,OAE5DC,EACFd,EAAM,MAAMc,EAAc,CACxB,GAAAL,EACA,GAAGb,EACH,GAAGA,GAAA,YAAAA,EAAM,KACX,CAAC,EAEDI,EAAM,QAAQS,CAAE,CAEpB,CAAC,EAEIF,CACT,EC5JA,IAAAQ,EAA+C,iBAKxC,IAAMC,EAAe,IAEfC,EAAa,CACxBC,EACAC,EAAoB,YACjB,CACH,GAAM,CAAE,OAAAC,EAAQ,SAAAC,CAAS,EAAIC,EAASJ,EAAcC,CAAS,EACvDI,KAAgB,UACpB,IAAI,GACN,EAAE,QAEIC,KAAmB,eACvB,CAACC,EAAiBC,EAAcV,IAAiB,CAC/C,GAAIO,EAAc,IAAIE,CAAO,EAC3B,OAGF,IAAME,EAAU,WAAW,IAAM,CAC/BJ,EAAc,OAAOE,CAAO,EAC5BG,EAAS,CACP,OACA,QAASH,CACX,CAAC,CACH,EAAGC,CAAW,EAEdH,EAAc,IAAIE,EAASE,CAAO,CACpC,EACA,CAAC,CACH,KAEA,aAAU,IAAM,CACd,GAAIN,EACF,OAGF,IAAMQ,EAAM,KAAK,IAAI,EACfC,EAAWV,EAAO,IAAKW,GAAM,CACjC,GAAIA,EAAE,WAAa,IACjB,OAGF,IAAMC,GACHD,EAAE,UAAY,GAAKA,EAAE,eAAiBF,EAAME,EAAE,WAEjD,GAAIC,EAAe,EAAG,CAChBD,EAAE,SACJE,EAAM,QAAQF,EAAE,EAAE,EAEpB,OAEF,OAAO,WAAW,IAAME,EAAM,QAAQF,EAAE,GAAIZ,CAAS,EAAGa,CAAY,CACtE,CAAC,EAED,MAAO,IAAM,CACXF,EAAS,QAASH,GAAYA,GAAW,aAAaA,CAAO,CAAC,CAChE,CACF,EAAG,CAACP,EAAQC,EAAUF,CAAS,CAAC,EAEhC,IAAMS,KAAW,eAAYM,EAAef,CAAS,EAAG,CAACA,CAAS,CAAC,EAE7DgB,KAAa,eAAY,IAAM,CACnCP,EAAS,CACP,OACA,KAAM,KAAK,IAAI,CACjB,CAAC,CACH,EAAG,CAACA,CAAQ,CAAC,EAEPQ,KAAe,eACnB,CAACX,EAAiBY,IAAmB,CACnCT,EAAS,CACP,OACA,MAAO,CAAE,GAAIH,EAAS,OAAAY,CAAO,CAC/B,CAAC,CACH,EACA,CAACT,CAAQ,CACX,EAEMU,KAAW,eAAY,IAAM,CAC7BjB,GACFO,EAAS,CAAE,OAA4B,KAAM,KAAK,IAAI,CAAE,CAAC,CAE7D,EAAG,CAACP,EAAUO,CAAQ,CAAC,EAEjBW,KAAkB,eACtB,CACEN,EACAO,IAKG,CACH,GAAM,CAAE,aAAAC,EAAe,GAAO,OAAAC,EAAS,EAAG,gBAAAC,CAAgB,EAAIH,GAAQ,CAAC,EAEjEI,EAAiBxB,EAAO,OAC3BW,IACEA,EAAE,UAAYY,MACZV,EAAM,UAAYU,IAAoBZ,EAAE,MAC/C,EACMc,EAAaD,EAAe,UAAWb,GAAMA,EAAE,KAAOE,EAAM,EAAE,EAC9Da,EAAeF,EAAe,OAClC,CAACX,EAAOc,IAAMA,EAAIF,GAAcZ,EAAM,OACxC,EAAE,OAOF,OALeW,EACZ,OAAQb,GAAMA,EAAE,OAAO,EACvB,MAAM,GAAIU,EAAe,CAACK,EAAe,CAAC,EAAI,CAAC,EAAGA,CAAY,CAAE,EAChE,OAAO,CAACE,EAAKjB,IAAMiB,GAAOjB,EAAE,QAAU,GAAKW,EAAQ,CAAC,CAGzD,EACA,CAACtB,CAAM,CACT,EAGA,sBAAU,IAAM,CACdA,EAAO,QAASa,GAAU,CACxB,GAAIA,EAAM,UACRT,EAAiBS,EAAM,GAAIA,EAAM,WAAW,MACvC,CAEL,IAAMN,EAAUJ,EAAc,IAAIU,EAAM,EAAE,EACtCN,IACF,aAAaA,CAAO,EACpBJ,EAAc,OAAOU,EAAM,EAAE,GAGnC,CAAC,CACH,EAAG,CAACb,EAAQI,CAAgB,CAAC,EAEtB,CACL,OAAAJ,EACA,SAAU,CACR,aAAAgB,EACA,WAAAD,EACA,SAAAG,EACA,gBAAAC,CACF,CACF,CACF,EL5HA,IAAOU,EAAQC", "names": ["headless_exports", "__export", "headless_default", "resolveValue", "toast", "useToaster", "useStore", "__toCommonJS", "isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "import_react", "TOAST_LIMIT", "DEFAULT_TOASTER_ID", "reducer", "state", "action", "toastLimit", "t", "toast", "toastId", "diff", "listeners", "defaultToasterState", "TOAST_LIMIT", "memoryState", "dispatch", "toasterId", "DEFAULT_TOASTER_ID", "id", "listener", "dispatchAll", "getToasterIdFromToastId", "createDispatch", "defaultTimeouts", "useStore", "toastOptions", "setState", "initial", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "createDispatch", "getToasterIdFromToastId", "toastId", "toasterId", "action", "dispatchAll", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "import_react", "REMOVE_DELAY", "useToaster", "toastOptions", "toasterId", "toasts", "pausedAt", "useStore", "toastTimeouts", "addToRemoveQueue", "toastId", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "dispatch", "now", "timeouts", "t", "durationLeft", "toast", "createDispatch", "startPause", "updateHeight", "height", "endPause", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "headless_default", "toast"]}