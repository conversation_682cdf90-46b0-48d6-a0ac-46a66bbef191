# Routes Structure

Cấu trúc routes được tổ chức theo từng module để dễ quản lý và bảo trì.

## Cấu trúc thư mục

```
routes/
├── client/                 # Routes cho phần client
│   ├── index.jsx          # Routes chính cho client (trang chủ, sản phẩm, giỏ hàng)
│   ├── authRoutes.jsx     # Routes xác thực (đăng nhập, đăng ký)
│   ├── userRoutes.jsx     # Routes cho user đã đăng nhập (profile, orders)
│   └── index.js           # Export file
├── admin/                  # Routes cho phần admin
│   ├── index.jsx          # Routes chính cho admin (dashboard)
│   ├── productRoutes.jsx  # Routes quản lý sản phẩm
│   ├── orderRoutes.jsx    # Routes quản lý đơn hàng
│   └── index.js           # Export file
├── AppRoutes.jsx          # Component routes chính
├── ProtectedRoute.jsx     # Component bảo vệ routes
├── RouteConfig.js         # Cấ<PERSON> hình routes và metadata
└── index.js               # Export tất cả
```

## Phân loại Routes

### Client Routes (`/src/routes/client/`)

#### 1. Public Routes (không yêu cầu đăng nhập)
- `/` - Trang chủ
- `/products` - Danh sách sản phẩm
- `/products/detail/:slug` - Chi tiết sản phẩm
- `/cart` - Giỏ hàng

#### 2. Auth Routes (xác thực)
- `/auth/login` - Đăng nhập
- `/auth/register` - Đăng ký
- `/auth/forgot-password` - Quên mật khẩu (sẽ thêm)
- `/auth/reset-password` - Đặt lại mật khẩu (sẽ thêm)

#### 3. Protected Routes (yêu cầu đăng nhập)
- `/checkout` - Thanh toán
- `/profile` - Thông tin cá nhân
- `/orders` - Lịch sử đơn hàng
- `/orders/:id` - Chi tiết đơn hàng

### Admin Routes (`/src/routes/admin/`)

#### 1. Dashboard Routes (yêu cầu quyền admin)
- `/admin` - Dashboard chính
- `/admin/reports` - Báo cáo thống kê

#### 2. Product Management Routes
- `/admin/products` - Danh sách sản phẩm
- `/admin/products/create` - Tạo sản phẩm mới
- `/admin/products/edit/:slug` - Chỉnh sửa sản phẩm
- `/admin/products/detail/:slug` - Chi tiết sản phẩm

#### 3. Order Management Routes
- `/admin/orders` - Danh sách đơn hàng
- `/admin/orders/:id` - Chi tiết đơn hàng
- `/admin/orders/:id/status` - Cập nhật trạng thái

#### 4. Other Management Routes
- `/admin/categories` - Quản lý danh mục
- `/admin/users` - Quản lý người dùng

## Protected Routes

Sử dụng component `ProtectedRoute` để bảo vệ các routes:

```jsx
<ProtectedRoute requireAuth={true}>
  <ComponentCanThiDangNhap />
</ProtectedRoute>

<ProtectedRoute requireAuth={true} requireAdmin={true}>
  <ComponentCanQuyenAdmin />
</ProtectedRoute>
```

## Route Configuration

File `RouteConfig.js` chứa:
- `ROUTE_PATHS`: Định nghĩa tất cả đường dẫn
- `ROUTE_META`: Metadata cho từng route (title, description, permissions)

## Sử dụng

```jsx
// Import routes
import { AppRoutes } from './routes';

// Sử dụng trong App.jsx
function App() {
  return (
    <Router>
      <AppRoutes />
    </Router>
  );
}
```

## Lưu ý

1. **Tách biệt rõ ràng**: Client và Admin routes được tách riêng hoàn toàn
2. **Bảo mật**: Sử dụng ProtectedRoute cho các trang yêu cầu xác thực
3. **Dễ mở rộng**: Có thể dễ dàng thêm routes mới vào từng module
4. **Lazy Loading**: Có thể áp dụng lazy loading cho từng module sau này
