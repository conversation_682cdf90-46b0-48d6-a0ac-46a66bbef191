import { Route } from 'react-router-dom';
import AdminLayout from '../../layouts/AdminLayout';
import ProtectedRoute from '../ProtectedRoute';

// Import các trang admin
import Dashboard from '../../pages/admin/Dashboard';
import ProductsManagement from '../../pages/admin/ProductsManagement';

// Routes cho phần admin (quản trị viên)
const AdminRoutes = () => {
  return (
    <Route path="/admin" element={
      <ProtectedRoute requireAuth={true} requireAdmin={true}>
        <AdminLayout />
      </ProtectedRoute>
    }>
      {/* Dashboard chính */}
      <Route index element={<Dashboard />} />
      
      {/* Quản lý sản phẩm */}
      <Route path="products" element={<ProductsManagement />} />
      
      {/* Quản lý danh mục */}
      <Route 
        path="categories" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Quản lý danh mục</h1>
            <p className="text-gray-600">Tính năng đang được phát triển...</p>
          </div>
        } 
      />
      
      {/* Quản lý đơn hàng */}
      <Route 
        path="orders" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Quản lý đơn hàng</h1>
            <p className="text-gray-600">Tính năng đang được phát triển...</p>
          </div>
        } 
      />
      
      {/* Quản lý người dùng */}
      <Route 
        path="users" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Quản lý người dùng</h1>
            <p className="text-gray-600">Tính năng đang được phát triển...</p>
          </div>
        } 
      />
      
      {/* Báo cáo thống kê */}
      <Route 
        path="reports" 
        element={
          <div className="p-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Báo cáo thống kê</h1>
            <p className="text-gray-600">Tính năng đang được phát triển...</p>
          </div>
        } 
      />
    </Route>
  );
};

export default AdminRoutes;
