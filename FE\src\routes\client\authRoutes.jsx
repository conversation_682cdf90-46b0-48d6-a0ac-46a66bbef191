import { Route } from 'react-router-dom';

// Import các trang xác thực
import Login from '../../pages/client/Login';
import Register from '../../pages/client/Register';

// Routes cho xác thực người dùng (không có layout)
const AuthRoutes = () => {
  return (
    <>
      {/* Trang đăng nhập */}
      <Route path="/auth/login" element={<Login />} />
      
      {/* Trang đăng ký */}
      <Route path="/auth/register" element={<Register />} />
      
      {/* Có thể thêm các routes khác */}
      {/* <Route path="/auth/forgot-password" element={<ForgotPassword />} /> */}
      {/* <Route path="/auth/reset-password" element={<ResetPassword />} /> */}
      {/* <Route path="/auth/verify-email" element={<VerifyEmail />} /> */}
    </>
  );
};

export default AuthRoutes;
